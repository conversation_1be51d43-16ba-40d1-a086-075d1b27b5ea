// Simple test script for auth functionality
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// Test JWT functionality
const JWT_SECRET = "synapse-ai-secret-key";
const JWT_EXPIRES_IN = "24h";

async function testAuth() {
  console.log('Testing JWT functionality...');
  
  try {
    // Test payload
    const payload = {
      userId: "test-user-123",
      email: "<EMAIL>",
      role: "user",
      tenantId: "default",
      permissions: ["widget:read"]
    };

    // Test JWT signing
    const token = jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: "synapse-ai",
      audience: "synapse-ai-client",
    });

    console.log('✅ JWT Token generated successfully');
    console.log('Token:', token.substring(0, 50) + '...');

    // Test JWT verification
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: "synapse-ai",
      audience: "synapse-ai-client",
    });

    console.log('✅ JWT Token verified successfully');
    console.log('Decoded payload:', decoded);

    // Test password hashing
    const password = "testpassword123";
    const hashedPassword = await bcrypt.hash(password, 12);
    console.log('✅ Password hashed successfully');

    // Test password verification
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('✅ Password verification:', isValid ? 'PASSED' : 'FAILED');

    console.log('\n🎉 All auth tests passed!');
    
  } catch (error) {
    console.error('❌ Auth test failed:', error.message);
  }
}

testAuth();
