import { z } from "zod";
import { redis } from "./redis";
import axios from "axios";
import * as crypto from "crypto";

// Tool definition schema
const ToolDefinitionSchema = z.object({
  name: z.string(),
  description: z.string(),
  parameters: z.record(z.union([z.string(), z.number(), z.boolean()])),
  type: z.enum(["api", "function", "database", "webhook"]),
  config: z.record(z.union([z.string(), z.number(), z.boolean()])),
  isActive: z.boolean(),
  createdAt: z.number(),
  updatedAt: z.number(),
});

export type ToolDefinition = z.infer<typeof ToolDefinitionSchema>;

// Built-in tools
class APITool {
  async execute(
    config: any,
    parameters: Record<string, any>,
    context: any,
  ): Promise<any> {
    const { url, method = "GET", headers = {}, timeout = 30000 } = config;

    const requestConfig: any = {
      method,
      url,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      timeout,
    };

    if (method !== "GET" && parameters) {
      requestConfig.data = parameters;
    } else if (method === "GET" && parameters) {
      requestConfig.params = parameters;
    }

    const response = await axios(requestConfig);
    return {
      status: response.status,
      data: response.data,
      headers: response.headers,
    };
  }
}

class DatabaseTool {
  async execute(
    config: any,
    parameters: Record<string, any>,
    context: any,
  ): Promise<any> {
    const { query, type = "redis" } = config;

    if (type === "redis") {
      // Execute Redis command
      const processedQuery = this.processTemplate(query, parameters);
      const [command, ...args] = processedQuery.split(" ");

      switch (command.toLowerCase()) {
        case "get":
          return await redis.get(args[0]);
        case "set":
          return await redis.set(args[0], args[1]);
        case "del":
          return await redis.del(args[0]);
        case "exists":
          return await redis.exists(args[0]);
        case "keys":
          return await redis.keys(args[0]);
        default:
          throw new Error(`Unsupported Redis command: ${command}`);
      }
    }

    throw new Error(`Unsupported database type: ${type}`);
  }

  private processTemplate(
    template: string,
    variables: Record<string, any>,
  ): string {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{${key}\\}`, "g");
      result = result.replace(regex, String(value));
    }
    return result;
  }
}

class WebhookTool {
  async execute(
    config: any,
    parameters: Record<string, any>,
    context: any,
  ): Promise<any> {
    const { url, method = "POST", headers = {}, secret } = config;

    const payload = {
      ...parameters,
      timestamp: Date.now(),
      executionId: context.executionId,
      userId: context.userId,
    };

    const requestHeaders: any = {
      "Content-Type": "application/json",
      ...headers,
    };

    // Add webhook signature if secret is provided
    if (secret) {
      const signature = crypto
        .createHmac("sha256", secret)
        .update(JSON.stringify(payload))
        .digest("hex");
      requestHeaders["X-Webhook-Signature"] = `sha256=${signature}`;
    }

    const response = await axios({
      method,
      url,
      data: payload,
      headers: requestHeaders,
      timeout: 30000,
    });

    return {
      status: response.status,
      data: response.data,
    };
  }
}

class FunctionTool {
  async execute(
    config: any,
    parameters: Record<string, any>,
    context: any,
  ): Promise<any> {
    const { code, language = "javascript" } = config;

    if (language === "javascript") {
      // Execute JavaScript code in a sandboxed environment
      // Note: In production, use a proper sandboxing solution like vm2
      const func = new Function(
        "parameters",
        "context",
        "redis",
        "axios",
        code,
      );
      return await func(parameters, context, redis, axios);
    }

    throw new Error(`Unsupported language: ${language}`);
  }
}

// Tool executor
export class ToolExecutor {
  private tools: Map<string, ToolDefinition> = new Map();
  private toolInstances: Map<string, any> = new Map();

  constructor() {
    // Register built-in tools
    this.toolInstances.set("api", new APITool());
    this.toolInstances.set("database", new DatabaseTool());
    this.toolInstances.set("webhook", new WebhookTool());
    this.toolInstances.set("function", new FunctionTool());
  }

  async registerTool(tool: ToolDefinition): Promise<void> {
    this.tools.set(tool.name, tool);
    await redis.setex(
      `synapse:tool:${tool.name}`,
      30 * 24 * 60 * 60,
      JSON.stringify(tool),
    );
  }

  async executeTool(
    toolName: string,
    parameters: Record<string, any>,
    context: any,
  ): Promise<any> {
    const tool = this.tools.get(toolName);
    if (!tool || !tool.isActive) {
      throw new Error(`Tool ${toolName} not found or inactive`);
    }

    const toolInstance = this.toolInstances.get(tool.type);
    if (!toolInstance) {
      throw new Error(`Tool type ${tool.type} not supported`);
    }

    try {
      const result = await toolInstance.execute(
        tool.config,
        parameters,
        context,
      );

      // Log tool execution
      await redis.lpush(
        `synapse:tool:log:${toolName}`,
        JSON.stringify({
          parameters,
          result,
          context: {
            executionId: context.executionId,
            userId: context.userId,
            timestamp: Date.now(),
          },
        }),
      );

      // Keep only last 100 executions
      await redis.ltrim(`synapse:tool:log:${toolName}`, 0, 99);

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Tool execution failed:`, error);

      // Log error
      await redis.lpush(
        `synapse:tool:error:${toolName}`,
        JSON.stringify({
          parameters,
          error: errorMessage,
          context: {
            executionId: context.executionId,
            userId: context.userId,
            timestamp: Date.now(),
          },
        }),
      );

      throw error;
    }
  }

  async getToolLogs(toolName: string, limit: number = 10): Promise<any[]> {
    const logs = await redis.lrange(`synapse:tool:log:${toolName}`, 0, limit - 1);
    return logs.map((log: string  ) => JSON.parse(log));
  }

  async getToolErrors(toolName: string, limit: number = 10): Promise<any[]> {
    const errors = await redis.lrange(
      `synapse:tool:error:${toolName}`,
      0,
      limit - 1,
    );
    return errors.map((error: string  ) => JSON.parse(error));
  }

  async loadToolsFromRedis(): Promise<void> {
    const keys = await redis.keys("synapse:tool:*");
    for (const key of keys) {
      if (key.includes(":log:") || key.includes(":error:")) continue;
      const data = await redis.get(key);
      if (data) {
        const tool = ToolDefinitionSchema.parse(JSON.parse(data));
        this.tools.set(tool.name, tool);
      }
    }
  }
}

export const toolExecutor = new ToolExecutor();
