import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { z } from "zod";
import { redis } from "./redis";

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || "synapse-ai-secret-key";
const JWT_EXPIRES_IN: string = process.env.JWT_EXPIRES_IN || "24h";
const REFRESH_TOKEN_EXPIRES_IN = "7d"; // 7 days for JWT
const REFRESH_TOKEN_EXPIRES_IN_SECONDS = 7 * 24 * 60 * 60; // 7 days in seconds for Redis

// User schema
const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  password: z.string().min(8),
  role: z.enum(["admin", "developer", "user"]),
  tenantId: z.string(),
  permissions: z.array(z.string()),
  createdAt: z.number(),
  updatedAt: z.number(),
  isActive: z.boolean().default(true),
});

const TokenPayloadSchema = z.object({
  userId: z.string(),
  email: z.string(),
  role: z.string(),
  tenantId: z.string(),
  permissions: z.array(z.string()),
  iat: z.number().optional(),
  exp: z.number().optional(),
});

export type User = z.infer<typeof UserSchema>;
export type TokenPayload = z.infer<typeof TokenPayloadSchema>;

// RBAC Permissions
export const PERMISSIONS = {
  // Agent permissions
  AGENT_CREATE: "agent:create",
  AGENT_READ: "agent:read",
  AGENT_UPDATE: "agent:update",
  AGENT_DELETE: "agent:delete",
  AGENT_DEPLOY: "agent:deploy",

  // Provider permissions
  PROVIDER_CREATE: "provider:create",
  PROVIDER_READ: "provider:read",
  PROVIDER_UPDATE: "provider:update",
  PROVIDER_DELETE: "provider:delete",
  PROVIDER_MANAGE: "provider:manage",

  // Analytics permissions
  ANALYTICS_READ: "analytics:read",
  ANALYTICS_EXPORT: "analytics:export",

  // Admin permissions
  ADMIN_USERS: "admin:users",
  ADMIN_TENANTS: "admin:tenants",
  ADMIN_SYSTEM: "admin:system",

  // Widget permissions
  WIDGET_CREATE: "widget:create",
  WIDGET_READ: "widget:read",
  WIDGET_UPDATE: "widget:update",
  WIDGET_DELETE: "widget:delete",
} as const;

// Role-based permission mapping
const ROLE_PERMISSIONS = {
  admin: Object.values(PERMISSIONS),
  developer: [
    PERMISSIONS.AGENT_CREATE,
    PERMISSIONS.AGENT_READ,
    PERMISSIONS.AGENT_UPDATE,
    PERMISSIONS.AGENT_DELETE,
    PERMISSIONS.AGENT_DEPLOY,
    PERMISSIONS.PROVIDER_READ,
    PERMISSIONS.PROVIDER_UPDATE,
    PERMISSIONS.ANALYTICS_READ,
    PERMISSIONS.WIDGET_CREATE,
    PERMISSIONS.WIDGET_READ,
    PERMISSIONS.WIDGET_UPDATE,
    PERMISSIONS.WIDGET_DELETE,
  ],
  user: [
    PERMISSIONS.AGENT_READ,
    PERMISSIONS.PROVIDER_READ,
    PERMISSIONS.ANALYTICS_READ,
    PERMISSIONS.WIDGET_READ,
  ],
};

export class AuthService {
  private readonly userKeyPrefix = "synapse:user:";
  private readonly sessionKeyPrefix = "synapse:session:";
  private readonly refreshTokenKeyPrefix = "synapse:refresh:";

  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }

  async verifyPassword(
    password: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  generateTokens(user: User): { accessToken: string; refreshToken: string } {
    const payload: TokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
      permissions: user.permissions,
    };

    const accessToken = jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: "synapse-ai",
      audience: "synapse-ai-client",
    } as any);

    const refreshToken = jwt.sign(
      { userId: user.id, type: "refresh" },
      JWT_SECRET,
      {
        expiresIn: REFRESH_TOKEN_EXPIRES_IN,
        issuer: "synapse-ai",
        audience: "synapse-ai-client",
      }
    );

    return { accessToken, refreshToken };
  }

  async verifyToken(token: string): Promise<TokenPayload | null> {
    try {
      const decoded = jwt.verify(token, JWT_SECRET, {
        issuer: "synapse-ai",
        audience: "synapse-ai-client",
      }) as any;

      return TokenPayloadSchema.parse(decoded);
    } catch (error) {
      console.error("Token verification failed:", error);
      return null;
    }
  }

  async createUser(
    userData: Omit<User, "id" | "createdAt" | "updatedAt" | "permissions">,
  ): Promise<User> {
    const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const hashedPassword = await this.hashPassword(userData.password);

    const user: User = {
      ...userData,
      id: userId,
      password: hashedPassword,
      permissions: ROLE_PERMISSIONS[userData.role] || [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    await redis.setex(
      `${this.userKeyPrefix}${userId}`,
      30 * 24 * 60 * 60, // 30 days
      JSON.stringify(user),
    );

    // Index by email for login
    await redis.setex(
      `${this.userKeyPrefix}email:${userData.email}`,
      30 * 24 * 60 * 60,
      userId,
    );

    return user;
  }

  async getUserById(userId: string): Promise<User | null> {
    try {
      const userData = await redis.get(`${this.userKeyPrefix}${userId}`);
      if (!userData) return null;

      return UserSchema.parse(JSON.parse(userData));
    } catch (error) {
      console.error("Error retrieving user:", error);
      return null;
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const userId = await redis.get(`${this.userKeyPrefix}email:${email}`);
      if (!userId) return null;

      return this.getUserById(userId);
    } catch (error) {
      console.error("Error retrieving user by email:", error);
      return null;
    }
  }

  async authenticateUser(
    email: string,
    password: string,
  ): Promise<{
    user: User;
    tokens: { accessToken: string; refreshToken: string };
  } | null> {
    const user = await this.getUserByEmail(email);
    if (!user || !user.isActive) return null;

    const isValidPassword = await this.verifyPassword(password, user.password);
    if (!isValidPassword) return null;

    const tokens = this.generateTokens(user);

    // Store refresh token
    await redis.setex(
      `${this.refreshTokenKeyPrefix}${user.id}`,
      REFRESH_TOKEN_EXPIRES_IN_SECONDS,
      tokens.refreshToken,
    );

    return { user: { ...user, password: "" }, tokens }; // Don't return password
  }

  async refreshAccessToken(
    refreshToken: string,
  ): Promise<{ accessToken: string; refreshToken: string } | null> {
    try {
      const decoded = jwt.verify(refreshToken, JWT_SECRET, {
        issuer: "synapse-ai",
        audience: "synapse-ai-client",
      }) as any;

      if (decoded.type !== "refresh") return null;

      const storedToken = await redis.get(
        `${this.refreshTokenKeyPrefix}${decoded.userId}`,
      );
      if (storedToken !== refreshToken) return null;

      const user = await this.getUserById(decoded.userId);
      if (!user || !user.isActive) return null;

      const tokens = this.generateTokens(user);

      // Update stored refresh token
      await redis.setex(
        `${this.refreshTokenKeyPrefix}${user.id}`,
        REFRESH_TOKEN_EXPIRES_IN_SECONDS,
        tokens.refreshToken,
      );

      return tokens;
    } catch (error) {
      console.error("Refresh token verification failed:", error);
      return null;
    }
  }

  async revokeRefreshToken(userId: string): Promise<void> {
    await redis.del(`${this.refreshTokenKeyPrefix}${userId}`);
  }

  async updateUserPermissions(
    userId: string,
    permissions: string[],
  ): Promise<boolean> {
    const user = await this.getUserById(userId);
    if (!user) return false;

    user.permissions = permissions;
    user.updatedAt = Date.now();

    await redis.setex(
      `${this.userKeyPrefix}${userId}`,
      30 * 24 * 60 * 60,
      JSON.stringify(user),
    );

    return true;
  }

  hasPermission(
    userPermissions: string[],
    requiredPermission: string,
  ): boolean {
    return userPermissions.includes(requiredPermission);
  }

  hasAnyPermission(
    userPermissions: string[],
    requiredPermissions: string[],
  ): boolean {
    return requiredPermissions.some((permission) =>
      userPermissions.includes(permission),
    );
  }

  hasAllPermissions(
    userPermissions: string[],
    requiredPermissions: string[],
  ): boolean {
    return requiredPermissions.every((permission) =>
      userPermissions.includes(permission),
    );
  }

  async createSession(
    userId: string,
    metadata: Record<string, any> = {},
  ): Promise<string> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const session = {
      sessionId,
      userId,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      metadata,
    };

    await redis.setex(
      `${this.sessionKeyPrefix}${sessionId}`,
      24 * 60 * 60, // 24 hours
      JSON.stringify(session),
    );

    return sessionId;
  }

  async getSession(sessionId: string): Promise<any | null> {
    try {
      const sessionData = await redis.get(
        `${this.sessionKeyPrefix}${sessionId}`,
      );
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error("Error retrieving session:", error);
      return null;
    }
  }

  async updateSessionActivity(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (session) {
      session.lastActivity = Date.now();
      await redis.setex(
        `${this.sessionKeyPrefix}${sessionId}`,
        24 * 60 * 60,
        JSON.stringify(session),
      );
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    await redis.del(`${this.sessionKeyPrefix}${sessionId}`);
  }
}

export const authService = new AuthService();
