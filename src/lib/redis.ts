import Redis from "ioredis";
import { z } from "zod";

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || "localhost",
  port: parseInt(process.env.REDIS_PORT || "6379"),
  password: process.env.REDIS_PASSWORD,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
};

// Create Redis instances
export const redis = new Redis(redisConfig);
export const redisPub = new Redis(redisConfig);
export const redisSub = new Redis(redisConfig);

// Session memory schema
const SessionMemorySchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  context: z.record(z.any()),
  messages: z.array(
    z.object({
      role: z.enum(["user", "assistant", "system"]),
      content: z.string(),
      timestamp: z.number(),
      metadata: z.record(z.any()).optional(),
    }),
  ),
  state: z.record(z.any()),
  createdAt: z.number(),
  updatedAt: z.number(),
  ttl: z.number(),
});

export type SessionMemory = z.infer<typeof SessionMemorySchema>;

// Session-aware memory engine
export class MemoryEngine {
  private readonly keyPrefix = "synapse:memory:";
  private readonly defaultTTL = 3600; // 1 hour

  async createSession(
    userId: string,
    initialContext: Record<string, any> = {},
  ): Promise<string> {
    const sessionId = `${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const memory: SessionMemory = {
      sessionId,
      userId,
      context: initialContext,
      messages: [],
      state: {},
      createdAt: Date.now(),
      updatedAt: Date.now(),
      ttl: this.defaultTTL,
    };

    await this.saveMemory(sessionId, memory);
    return sessionId;
  }

  async getMemory(sessionId: string): Promise<SessionMemory | null> {
    try {
      const data = await redis.get(this.getKey(sessionId));
      if (!data) return null;

      const parsed = JSON.parse(data);
      return SessionMemorySchema.parse(parsed);
    } catch (error) {
      console.error("Error retrieving memory:", error);
      return null;
    }
  }

  async saveMemory(sessionId: string, memory: SessionMemory): Promise<void> {
    try {
      memory.updatedAt = Date.now();
      const key = this.getKey(sessionId);
      await redis.setex(key, memory.ttl, JSON.stringify(memory));
    } catch (error) {
      console.error("Error saving memory:", error);
      throw error;
    }
  }

  async addMessage(
    sessionId: string,
    role: "user" | "assistant" | "system",
    content: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const memory = await this.getMemory(sessionId);
    if (!memory) throw new Error("Session not found");

    memory.messages.push({
      role,
      content,
      timestamp: Date.now(),
      metadata,
    });

    await this.saveMemory(sessionId, memory);
  }

  async updateContext(
    sessionId: string,
    context: Record<string, any>,
  ): Promise<void> {
    const memory = await this.getMemory(sessionId);
    if (!memory) throw new Error("Session not found");

    memory.context = { ...memory.context, ...context };
    await this.saveMemory(sessionId, memory);
  }

  async updateState(
    sessionId: string,
    state: Record<string, any>,
  ): Promise<void> {
    const memory = await this.getMemory(sessionId);
    if (!memory) throw new Error("Session not found");

    memory.state = { ...memory.state, ...state };
    await this.saveMemory(sessionId, memory);
  }

  async extendTTL(sessionId: string, ttl: number): Promise<void> {
    const memory = await this.getMemory(sessionId);
    if (!memory) throw new Error("Session not found");

    memory.ttl = ttl;
    await this.saveMemory(sessionId, memory);
  }

  async deleteSession(sessionId: string): Promise<void> {
    await redis.del(this.getKey(sessionId));
  }

  async getUserSessions(userId: string): Promise<string[]> {
    const pattern = `${this.keyPrefix}${userId}_*`;
    const keys = await redis.keys(pattern);
    return keys.map((key) => key.replace(this.keyPrefix, ""));
  }

  private getKey(sessionId: string): string {
    return `${this.keyPrefix}${sessionId}`;
  }
}

export const memoryEngine = new MemoryEngine();
