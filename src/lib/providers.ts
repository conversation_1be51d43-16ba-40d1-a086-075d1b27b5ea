import axios from "axios";
import { z } from "zod";
import { redis } from "./redis";

// Provider configuration schema
export const ProviderConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(["openai", "anthropic", "google", "custom"]),
  apiKey: z.string(),
  baseUrl: z.string().optional(),
  priority: z.number().min(1),
  status: z.enum(["active", "inactive", "error"]),
  costPerToken: z.number().min(0),
  maxTokens: z.number().optional(),
  timeout: z.number().default(30000),
  retries: z.number().default(3),
  capabilities: z.array(z.string()).default([]),
  metadata: z.record(z.any()).optional(),
});

export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;

export const ProviderRequestSchema = z.object({
  prompt: z.string(),
  maxTokens: z.number().optional(),
  temperature: z.number().min(0).max(2).optional(),
  model: z.string().optional(),
  context: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
});

export type ProviderRequest = z.infer<typeof ProviderRequestSchema>;

// Provider response schema
export const ProviderResponseSchema = z.object({
  content: z.string(),
  usage: z.object({
    promptTokens: z.number(),
    completionTokens: z.number(),
    totalTokens: z.number(),
  }),
  model: z.string(),
  finishReason: z.string(),
  metadata: z.record(z.any()).optional(),
});

export type ProviderResponse = z.infer<typeof ProviderResponseSchema>;

// Provider metrics schema
export const PerformanceMetricsSchema = z.object({
  providerId: z.string(),
  averageResponseTime: z.number(),
  successRate: z.number(),
  lastUpdated: z.number(),
});

export type PerformanceMetrics = z.infer<typeof PerformanceMetricsSchema>;

// Task analysis interface
interface TaskAnalysis {
  domain: string;
  requiresReasoning: boolean;
  requiresCreativity: boolean;
  requiresFactualAccuracy: boolean;
  complexity: number;
}

// AI Provider interfaces
interface AIRequest {
  prompt: string;
  maxTokens?: number;
  temperature?: number;
  model?: string;
  metadata?: Record<string, any>;
}

interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
  metadata?: Record<string, any>;
}

// Provider implementations
class OpenAIProvider {
  constructor(private config: ProviderConfig) {}

  async generateCompletion(request: AIRequest): Promise<AIResponse> {
    const response = await axios.post(
      this.config.baseUrl || "https://api.openai.com/v1/chat/completions",
      {
        model: request.model || "gpt-4",
        messages: [{ role: "user", content: request.prompt }],
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
      },
      {
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`,
          "Content-Type": "application/json",
        },
        timeout: this.config.timeout,
      },
    );

    const choice = response.data.choices[0];
    return {
      content: choice.message.content,
      usage: {
        promptTokens: response.data.usage.prompt_tokens,
        completionTokens: response.data.usage.completion_tokens,
        totalTokens: response.data.usage.total_tokens,
      },
      model: response.data.model,
      finishReason: choice.finish_reason,
      metadata: { providerId: this.config.id },
    };
  }
}

class AnthropicProvider {
  constructor(private config: ProviderConfig) {}

  async generateCompletion(request: AIRequest): Promise<AIResponse> {
    const response = await axios.post(
      this.config.baseUrl || "https://api.anthropic.com/v1/messages",
      {
        model: request.model || "claude-3-sonnet-20240229",
        max_tokens: request.maxTokens || 1000,
        messages: [{ role: "user", content: request.prompt }],
      },
      {
        headers: {
          "x-api-key": this.config.apiKey,
          "Content-Type": "application/json",
          "anthropic-version": "2023-06-01",
        },
        timeout: this.config.timeout,
      },
    );

    return {
      content: response.data.content[0].text,
      usage: {
        promptTokens: response.data.usage.input_tokens,
        completionTokens: response.data.usage.output_tokens,
        totalTokens:
          response.data.usage.input_tokens + response.data.usage.output_tokens,
      },
      model: response.data.model,
      finishReason: response.data.stop_reason,
      metadata: { providerId: this.config.id },
    };
  }
}

class GoogleProvider {
  constructor(private config: ProviderConfig) {}

  async generateCompletion(request: AIRequest): Promise<AIResponse> {
    const response = await axios.post(
      this.config.baseUrl ||
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${this.config.apiKey}`,
      {
        contents: [{ parts: [{ text: request.prompt }] }],
        generationConfig: {
          maxOutputTokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.7,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: this.config.timeout,
      },
    );

    const candidate = response.data.candidates[0];
    return {
      content: candidate.content.parts[0].text,
      usage: {
        promptTokens: response.data.usageMetadata?.promptTokenCount || 0,
        completionTokens: response.data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.data.usageMetadata?.totalTokenCount || 0,
      },
      model: "gemini-pro",
      finishReason: candidate.finishReason,
      metadata: { providerId: this.config.id },
    };
  }
}

// Provider analyzer with intelligent routing
export class ProviderAnalyzer {
  private providers: Map<string, ProviderConfig> = new Map();
  private providerInstances: Map<string, any> = new Map();
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();

  async addProvider(config: ProviderConfig): Promise<void> {
    const tenantId = config.metadata?.tenantId;
    if (!tenantId) {
      throw new Error("Tenant ID is required");
    }

    this.providers.set(config.id, config);

    // Create provider instance based on type
    let instance;
    switch (config.type) {
      case "openai":
        instance = new OpenAIProvider(config);
        break;
      case "anthropic":
        instance = new AnthropicProvider(config);
        break;
      case "google":
        instance = new GoogleProvider(config);
        break;
      default:
        throw new Error(`Unsupported provider type: ${config.type}`);
    }

    this.providerInstances.set(config.id, instance);

    // Initialize metrics
    this.performanceMetrics.set(config.id, {
      providerId: config.id,
      averageResponseTime: 0,
      successRate: 100,
      lastUpdated: Date.now(),
    });

    // Save to Redis
    await redis.setex(
      `synapse:provider:${tenantId}:${config.id}`,
      30 * 24 * 60 * 60,
      JSON.stringify(config),
    );
  }

  getAllProviders(): ProviderConfig[] {
    return Array.from(this.providers.values());
  }

  getProvider(id: string): ProviderConfig | undefined {
    return this.providers.get(id);
  }

  getAllMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.performanceMetrics);
  }

  getMetrics(providerId: string): PerformanceMetrics | undefined {
    return this.performanceMetrics.get(providerId);
  }

  async removeProvider(providerId: string): Promise<void> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    // Remove from maps
    this.providers.delete(providerId);
    this.providerInstances.delete(providerId);
    this.performanceMetrics.delete(providerId);

    // Remove from Redis if tenantId is available
    const tenantId = provider.metadata?.tenantId;
    if (tenantId) {
      await redis.del(`synapse:provider:${tenantId}:${providerId}`);
    }
  }

  async updateProvider(config: ProviderConfig): Promise<void> {
    const existingProvider = this.providers.get(config.id);
    if (!existingProvider) {
      throw new Error(`Provider ${config.id} not found`);
    }

    // Update the provider
    await this.addProvider(config);
  }

  getActiveProviders(): ProviderConfig[] {
    return this.getAllProviders().filter(p => p.status === "active");
  }

  getProvidersByType(type: string): ProviderConfig[] {
    return this.getAllProviders().filter(p => p.type === type);
  }

  async executeRequest(
    providerId: string,
    request: ProviderRequest,
  ): Promise<ProviderResponse> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    if (provider.status !== "active") {
      throw new Error(`Provider ${providerId} is not active (status: ${provider.status})`);
    }

    const instance = this.providerInstances.get(providerId);
    if (!instance) {
      throw new Error(`Provider instance ${providerId} not found`);
    }

    const maxRetries = provider.retries || 3;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      const startTime = Date.now();
      let success = false;

      try {
        const response = await instance.generateCompletion(request);
        success = true;

        // Validate response
        const validatedResponse = this.validateProviderResponse(response, provider);

        // Update metrics on success
        const responseTime = Date.now() - startTime;
        this.updatePerformanceMetrics(provider.id, responseTime, success);

        return validatedResponse;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        const responseTime = Date.now() - startTime;

        // Update metrics on failure
        this.updatePerformanceMetrics(provider.id, responseTime, success);

        console.error(`Provider ${provider.id} request failed (attempt ${attempt + 1}/${maxRetries + 1}):`, lastError.message);

        // If this is the last attempt, don't retry
        if (attempt === maxRetries) {
          break;
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new Error(`Provider ${provider.id} failed after ${maxRetries + 1} attempts. Last error: ${lastError?.message}`);
  }

  async selectBestProvider(request: ProviderRequest): Promise<string | null> {
    const providers = this.getActiveProviders();
    if (providers.length === 0) {
      return null;
    }

    // Analyze the task to determine best provider
    const taskAnalysis = this.analyzeTask(request);

    // Calculate scores for each provider
    const scoredProviders = providers.map(provider => ({
      provider,
      score: this.calculateProviderScore(provider, taskAnalysis)
    }));

    // Sort by score (descending) then by priority (ascending)
    scoredProviders.sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      return a.provider.priority - b.provider.priority;
    });

    return scoredProviders[0].provider.id;
  }

  private analyzeTask(request: ProviderRequest): TaskAnalysis {
    if (!request.prompt || request.prompt.trim().length === 0) {
      throw new Error("Invalid request: prompt is required");
    }

    const prompt = request.prompt.toLowerCase();

    return {
      domain: this.detectDomain(prompt),
      requiresReasoning: this.requiresReasoning(prompt),
      requiresCreativity: this.requiresCreativity(prompt),
      requiresFactualAccuracy: this.requiresFactualAccuracy(prompt),
      complexity: this.calculateComplexity(prompt),
    };
  }

  private detectDomain(prompt: string): string {
    // Coding domain keywords
    const codingKeywords = ["code", "programming", "function", "algorithm", "debug", "syntax", "api", "database", "sql", "javascript", "python", "typescript", "react", "node"];
    if (codingKeywords.some(keyword => prompt.includes(keyword))) return "coding";

    // Creative domain keywords
    const creativeKeywords = ["creative", "story", "poem", "write", "imagine", "fiction", "character", "plot", "narrative", "artistic"];
    if (creativeKeywords.some(keyword => prompt.includes(keyword))) return "creative";

    // Factual domain keywords
    const factualKeywords = ["fact", "information", "research", "data", "statistics", "history", "science", "definition", "explain"];
    if (factualKeywords.some(keyword => prompt.includes(keyword))) return "factual";

    return "general";
  }

  private requiresReasoning(prompt: string): boolean {
    const reasoningKeywords = ["analyze", "explain", "why", "how", "compare", "evaluate", "reason", "logic", "because", "therefore", "conclude"];
    return reasoningKeywords.some(keyword => prompt.includes(keyword));
  }

  private requiresCreativity(prompt: string): boolean {
    const creativityKeywords = ["creative", "imagine", "story", "invent", "design", "brainstorm", "original", "unique", "artistic", "innovative"];
    return creativityKeywords.some(keyword => prompt.includes(keyword));
  }

  private requiresFactualAccuracy(prompt: string): boolean {
    const factualKeywords = ["fact", "accurate", "information", "correct", "true", "verify", "source", "evidence", "data", "research"];
    return factualKeywords.some(keyword => prompt.includes(keyword));
  }

  private calculateComplexity(prompt: string): number {
    return Math.min(prompt.length / 100, 10); // Simple complexity based on length
  }

  private calculateProviderScore(
    provider: ProviderConfig,
    taskAnalysis: TaskAnalysis,
  ): number {
    let score = 0;

    // Base score from provider capabilities
    if (provider.capabilities.includes(taskAnalysis.domain)) score += 0.3;
    if (provider.capabilities.includes("reasoning") && taskAnalysis.requiresReasoning)
      score += 0.2;
    if (provider.capabilities.includes("creative") && taskAnalysis.requiresCreativity)
      score += 0.2;
    if (provider.capabilities.includes("factual") && taskAnalysis.requiresFactualAccuracy)
      score += 0.2;

    // Performance metrics bonus
    const metrics = this.performanceMetrics.get(provider.id);
    if (metrics) {
      score += (metrics.successRate / 100) * 0.1; // Up to 0.1 bonus for high success rate
    }

    return Math.max(0, Math.min(1, score)); // Clamp between 0 and 1
  }

  private updatePerformanceMetrics(
    providerId: string,
    responseTime: number,
    success: boolean,
  ): void {
    const metrics = this.performanceMetrics.get(providerId);
    if (!metrics) return;

    // Use exponential moving average for response time (more recent values have higher weight)
    const alpha = 0.3; // Smoothing factor
    metrics.averageResponseTime =
      metrics.averageResponseTime === 0
        ? responseTime
        : alpha * responseTime + (1 - alpha) * metrics.averageResponseTime;

    // Update success rate using exponential moving average
    const successValue = success ? 100 : 0;
    metrics.successRate =
      alpha * successValue + (1 - alpha) * metrics.successRate;

    metrics.lastUpdated = Date.now();
    this.performanceMetrics.set(providerId, metrics);
  }

  private validateProviderResponse(
    response: any,
    provider: ProviderConfig,
  ): ProviderResponse {
    try {
      const validatedResponse = ProviderResponseSchema.parse(response);

      // Additional production validations
      if (
        !validatedResponse.content ||
        validatedResponse.content.trim().length === 0
      ) {
        throw new Error("Provider returned empty content");
      }

      if (
        !validatedResponse.usage ||
        validatedResponse.usage.totalTokens <= 0
      ) {
        throw new Error("Provider returned invalid usage data");
      }

      if (validatedResponse.usage.totalTokens > 10000) {
        console.warn(
          `High token usage detected: ${validatedResponse.usage.totalTokens} tokens`,
        );
      }

      return validatedResponse;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(
        `Provider response validation failed for ${provider.name}:`,
        errorMessage,
      );
      throw new Error(`Invalid provider response: ${errorMessage}`);
    }
  }
}

export class ProviderExecutor {
  constructor(private analyzer: ProviderAnalyzer) {}

  async executeRequest(request: {
    metadata: Record<string, any>;
    providerRequest: ProviderRequest;
  }): Promise<ProviderResponse> {
    const providerId = request.metadata.providerId;

    if (!providerId) {
      // Auto-select provider
      const selectedProviderId = await this.analyzer.selectBestProvider(request.providerRequest);
      if (!selectedProviderId) {
        throw new Error("No available providers");
      }
      return this.analyzer.executeRequest(selectedProviderId, request.providerRequest);
    }

    return this.analyzer.executeRequest(providerId, request.providerRequest);
  }
}

// Initialize provider system
export const providerAnalyzer = new ProviderAnalyzer();
export const providerExecutor = new ProviderExecutor(providerAnalyzer);

// Export for backward compatibility
export const providerSelector = providerAnalyzer;