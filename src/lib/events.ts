import { redis, redisPub, redisSub } from "./redis";
import { z } from "zod";

// Event schemas
const EventSchema = z.object({
  id: z.string(),
  type: z.string(),
  data: z.record(z.any()),
  timestamp: z.number(),
  source: z.string(),
  tenantId: z.string().optional(),
  userId: z.string().optional(),
});

export type Event = z.infer<typeof EventSchema>;

// Event handler type
type EventHandler = (event: Event) => Promise<void> | void;

// Event bus implementation
export class EventBus {
  private handlers: Map<string, EventHandler[]> = new Map();
  private isSubscribed = false;

  constructor() {
    this.initializeSubscription();
  }

  private async initializeSubscription(): Promise<void> {
    if (this.isSubscribed) return;

    try {
      // Subscribe to all SynapseAI events
      await redisSub.psubscribe("synapse:events:*");
      
      redisSub.on("pmessage", async (pattern, channel, message) => {
        try {
          const event = EventSchema.parse(JSON.parse(message));
          await this.handleEvent(event);
        } catch (error) {
          console.error("Error processing event:", error);
        }
      });

      this.isSubscribed = true;
      console.log("Event bus subscription initialized");
    } catch (error) {
      console.error("Failed to initialize event subscription:", error);
    }
  }

  async emit(
    type: string,
    data: Record<string, any>,
    options: {
      tenantId?: string;
      userId?: string;
      source?: string;
    } = {},
  ): Promise<void> {
    const event: Event = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: Date.now(),
      source: options.source || "synapse-ai",
      tenantId: options.tenantId,
      userId: options.userId,
    };

    try {
      // Publish to Redis
      const channel = `synapse:events:${type}`;
      await redisPub.publish(channel, JSON.stringify(event));

      // Store event for history (with TTL)
      await redis.setex(
        `synapse:event:${event.id}`,
        24 * 60 * 60, // 24 hours
        JSON.stringify(event),
      );

      // Add to event stream for tenant/user
      if (event.tenantId) {
        await redis.lpush(
          `synapse:events:tenant:${event.tenantId}`,
          JSON.stringify(event),
        );
        await redis.ltrim(`synapse:events:tenant:${event.tenantId}`, 0, 999); // Keep last 1000 events
      }

      if (event.userId) {
        await redis.lpush(
          `synapse:events:user:${event.userId}`,
          JSON.stringify(event),
        );
        await redis.ltrim(`synapse:events:user:${event.userId}`, 0, 999); // Keep last 1000 events
      }
    } catch (error) {
      console.error("Failed to emit event:", error);
      throw error;
    }
  }

  on(eventType: string, handler: EventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler);
  }

  off(eventType: string, handler: EventHandler): void {
    const handlers = this.handlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private async handleEvent(event: Event): Promise<void> {
    const handlers = this.handlers.get(event.type) || [];
    
    // Execute all handlers for this event type
    await Promise.allSettled(
      handlers.map(async (handler) => {
        try {
          await handler(event);
        } catch (error) {
          console.error(`Event handler error for ${event.type}:`, error);
        }
      }),
    );
  }

  async getEventHistory(
    tenantId?: string,
    userId?: string,
    limit: number = 100,
  ): Promise<Event[]> {
    let key: string;
    if (tenantId) {
      key = `synapse:events:tenant:${tenantId}`;
    } else if (userId) {
      key = `synapse:events:user:${userId}`;
    } else {
      throw new Error("Either tenantId or userId must be provided");
    }

    const events = await redis.lrange(key, 0, limit - 1);
      console.log(events);
    return events.map((event: string) => JSON.parse(event));
  }

  async getEventsByType(
    eventType: string,
    tenantId?: string,
    limit: number = 100,
  ): Promise<Event[]> {
    const allEvents = await this.getEventHistory(tenantId, undefined, limit * 2);
    return allEvents
      .filter((event) => event.type === eventType)
      .slice(0, limit);
  }
}

// Global event bus instance
export const eventBus = new EventBus();

// Built-in event handlers
eventBus.on("workflow.execution.started", async (event) => {
  console.log(`Workflow execution started: ${event.data.workflowId}`);
});

eventBus.on("workflow.execution.completed", async (event) => {
  console.log(
    `Workflow execution completed: ${event.data.workflowId} in ${event.data.duration}ms`,
  );
});

eventBus.on("workflow.execution.failed", async (event) => {
  console.error(
    `Workflow execution failed: ${event.data.workflowId} - ${event.data.error}`,
  );
});

eventBus.on("ai.completion", async (event) => {
  console.log(
    `AI completion: ${event.data.providerId} - ${event.data.usage.totalTokens} tokens`,
  );
});

eventBus.on("tool.execution", async (event) => {
  console.log(`Tool executed: ${event.data.toolName}`);
});

eventBus.on("human.intervention.required", async (event) => {
  console.log(
    `Human intervention required for execution: ${event.data.executionId}`,
  );
  // In a real implementation, this would trigger notifications
});







