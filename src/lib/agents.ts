
import { z } from "zod";
import { redis } from "./redis";
import { provider<PERSON><PERSON><PERSON><PERSON>, providerExecutor } from "./providers";
import { toolExecutor } from "./tools";
import { eventBus } from "./events";

// Workflow node schemas
const NodeSchema = z.object({
  id: z.string(),
  type: z.enum(["input", "ai", "tool", "condition", "output", "human"]),
  position: z.object({ x: z.number(), y: z.number() }),
  data: z.record(z.any()),
});

const EdgeSchema = z.object({
  id: z.string(),
  source: z.string(),
  target: z.string(),
  condition: z.string().optional(),
});

export const WorkflowSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  nodes: z.array(NodeSchema),
  edges: z.array(EdgeSchema),
  version: z.number(),
  isActive: z.boolean(),
  settings: z.record(z.any()).optional(),
  createdAt: z.number(),
  updatedAt: z.number(),
});

export type Node = z.infer<typeof NodeSchema>;
export type Edge = z.infer<typeof EdgeSchema>;
export type Workflow = z.infer<typeof WorkflowSchema>;

// Execution context
interface ExecutionContext {
  sessionId: string;
  userId: string;
  tenantId: string;
  variables: Record<string, any>;
  currentNodeId: string;
  executionId: string;
  startTime: number;
  metadata: Record<string, any>;
}

// Execution result
interface ExecutionResult {
  success: boolean;
  output: any;
  error?: string;
  nextNodeId?: string;
  metadata?: Record<string, any>;
}

// Node executors
class NodeExecutor {
  async executeInputNode(
    node: Node,
    context: ExecutionContext,
    input: any,
  ): Promise<ExecutionResult> {
    // Input nodes just pass through the input
    return {
      success: true,
      output: input,
      metadata: { nodeType: "input", nodeId: node.id },
    };
  }

  async executeAINode(
    node: Node,
    context: ExecutionContext,
    input: any,
  ): Promise<ExecutionResult> {
    try {
      const { provider, prompt, model, temperature, maxTokens, priority } = node.data as {
        provider: string;
        prompt: string;
        model: string;
        temperature: number;
        maxTokens: number;
        priority: number;
      };

      // Select provider if not specified
      const providerId =
        provider ||
        (await providerAnalyzer.selectBestProvider({
          prompt: input,
          model,
          temperature,
          maxTokens,
          metadata: { priority: priority || 1 },
        }));

      if (!providerId) {
        throw new Error("No available AI provider");
      }

      // Process prompt template
      const processedPrompt = this.processTemplate(prompt || "{input}", {
        input,
        ...context.variables,
      });

      // Execute AI request
      const providerRequest = await providerExecutor.executeRequest({
        metadata: { nodeId: node.id, executionId: context.executionId, providerId },
        providerRequest: {
          prompt: processedPrompt,
          model: model || "gpt-4",
          temperature: temperature || 0.7,
          maxTokens: maxTokens || 1000,
        },
      });

      // Emit event
      await eventBus.emit("ai.completion", {
        executionId: context.executionId,
        nodeId: node.id,
        providerId,
        input: processedPrompt,
        output: providerRequest.content,
        usage: providerRequest.usage,
        timestamp: Date.now(),
      });

      return {
        success: true,
        output: providerRequest.content,
        metadata: {
          nodeType: "ai",
          nodeId: node.id,
          providerId,
          usage: providerRequest.usage,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`AI node execution failed:`, error);

      return {
        success: false,
        output: null,
        error: errorMessage,
        metadata: { nodeType: "ai", nodeId: node.id },
      };
    }
  }

  async executeToolNode(
    node: Node,
    context: ExecutionContext,
    input: any,
  ): Promise<ExecutionResult> {
    try {
      const { toolName, parameters } = node.data;

      // Process parameters template
      const processedParams = this.processTemplate(
        JSON.stringify(parameters || {}),
        { input, ...context.variables },
      );

      const parsedParams = JSON.parse(processedParams);

      // Execute tool
      const result = await toolExecutor.executeTool(
        toolName,
        parsedParams,
        context,
      );

      // Emit event
      await eventBus.emit("tool.execution", {
        executionId: context.executionId,
        nodeId: node.id,
        toolName,
        parameters: parsedParams,
        result,
        timestamp: Date.now(),
      });

      return {
        success: true,
        output: result,
        metadata: { nodeType: "tool", nodeId: node.id, toolName },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Tool node execution failed:`, error);

      return {
        success: false,
        output: null,
        error: errorMessage,
        metadata: { nodeType: "tool", nodeId: node.id },
      };
    }
  }

  async executeConditionNode(
    node: Node,
    context: ExecutionContext,
    input: any,
  ): Promise<ExecutionResult> {
    try {
      const { condition } = node.data;

      // Simple condition evaluation (in production, use a safe expression evaluator)
      const result = this.evaluateCondition(condition, {
        input,
        ...context.variables,
      });

      return {
        success: true,
        output: result,
        metadata: { nodeType: "condition", nodeId: node.id, condition },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Condition node execution failed:`, error);

      return {
        success: false,
        output: false,
        error: errorMessage,
        metadata: { nodeType: "condition", nodeId: node.id },
      };
    }
  }

  async executeHumanNode(
    node: Node,
    context: ExecutionContext,
    input: any,
  ): Promise<ExecutionResult> {
    // Human-in-the-loop node - pause execution and wait for human input
    const { message, timeout } = node.data;

    // Emit event for human intervention
    await eventBus.emit("human.intervention.required", {
      executionId: context.executionId,
      nodeId: node.id,
      message: message || "Human input required",
      input,
      timeout: timeout || 300000, // 5 minutes default
      timestamp: Date.now(),
    });

    // Store execution state for resumption
    await redis.setex(
      `synapse:execution:paused:${context.executionId}`,
      timeout || 300,
      JSON.stringify({
        context,
        nodeId: node.id,
        input,
        pausedAt: Date.now(),
      }),
    );

    return {
      success: true,
      output: "PAUSED_FOR_HUMAN_INPUT",
      metadata: {
        nodeType: "human",
        nodeId: node.id,
        status: "waiting",
      },
    };
  }

  async executeOutputNode(
    node: Node,
    context: ExecutionContext,
    input: any,
  ): Promise<ExecutionResult> {
    // Output nodes format and return the final result
    const { format } = node.data;

    let output = input;
    if (format) {
      output = this.processTemplate(format, { input, ...context.variables });
    }

    return {
      success: true,
      output,
      metadata: { nodeType: "output", nodeId: node.id },
    };
  }

  private processTemplate(
    template: string,
    variables: Record<string, any>,
  ): string {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{${key}\\}`, "g");
      result = result.replace(regex, String(value));
    }
    return result;
  }

  private evaluateCondition(
    condition: string,
    variables: Record<string, any>,
  ): boolean {
    // Simple condition evaluation - in production, use a safe expression evaluator
    // This is a basic implementation for demonstration
    try {
      const processedCondition = this.processTemplate(condition, variables);
      // For safety, only allow basic comparisons
      const safeCondition = processedCondition.replace(
        /[^a-zA-Z0-9\s<>=!&|()"'.-]/g,
        "",
      );
      return eval(safeCondition);
    } catch {
      return false;
    }
  }
}

// Agent executor
export class AgentExecutor {
  private workflows: Map<string, Workflow> = new Map();
  private nodeExecutor = new NodeExecutor();

  async registerWorkflow(workflow: Workflow): Promise<void> {
    this.workflows.set(workflow.id, workflow);
    await redis.setex(
      `synapse:workflow:${workflow.id}`,
      30 * 24 * 60 * 60,
      JSON.stringify(workflow),
    );
  }

  async executeWorkflow(
    workflowId: string,
    input: any,
    context: Partial<ExecutionContext>,
  ): Promise<any> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || !workflow.isActive) {
      throw new Error(`Workflow ${workflowId} not found or inactive`);
    }

    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullContext: ExecutionContext = {
      sessionId: context.sessionId || "default",
      userId: context.userId || "anonymous",
      tenantId: context.tenantId || "default",
      variables: context.variables || {},
      currentNodeId: "",
      executionId,
      startTime: Date.now(),
      metadata: context.metadata || {},
    };

    // Emit execution start event
    await eventBus.emit("workflow.execution.started", {
      workflowId,
      executionId,
      input,
      context: fullContext,
      timestamp: Date.now(),
    });

    try {
      // Find start node (input type)
      const startNode = workflow.nodes.find((node: Node) => node.type === "input");
      if (!startNode) {
        throw new Error("No input node found in workflow");
      }

      const result = await this.executeNode(
        startNode,
        workflow,
        fullContext,
        input,
      );

      // Emit execution complete event
      await eventBus.emit("workflow.execution.completed", {
        workflowId,
        executionId,
        result,
        duration: Date.now() - fullContext.startTime,
        timestamp: Date.now(),
      });

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Workflow execution failed:`, error);

      // Emit execution error event
      await eventBus.emit("workflow.execution.failed", {
        workflowId,
        executionId,
        error: errorMessage,
        duration: Date.now() - fullContext.startTime,
        timestamp: Date.now(),
      });

      throw error;
    }
  }

  private async executeNode(
    node: Node,
    workflow: Workflow,
    context: ExecutionContext,
    input: any,
  ): Promise<any> {
    context.currentNodeId = node.id;

    // Execute node based on type
    let result: ExecutionResult;
    switch (node.type) {
      case "input":
        result = await this.nodeExecutor.executeInputNode(node, context, input);
        break;
      case "ai":
        result = await this.nodeExecutor.executeAINode(node, context, input);
        break;
      case "tool":
        result = await this.nodeExecutor.executeToolNode(node, context, input);
        break;
      case "condition":
        result = await this.nodeExecutor.executeConditionNode(
          node,
          context,
          input,
        );
        break;
      case "human":
        result = await this.nodeExecutor.executeHumanNode(node, context, input);
        break;
      case "output":
        result = await this.nodeExecutor.executeOutputNode(
          node,
          context,
          input,
        );
        break;
      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }

    // If execution failed, stop here
    if (!result.success) {
      throw new Error(result.error || "Node execution failed");
    }

    // If this is a human node and paused, return special status
    if (result.output === "PAUSED_FOR_HUMAN_INPUT") {
      return { status: "paused", executionId: context.executionId };
    }

    // If this is an output node, return the result
    if (node.type === "output") {
      return result.output;
    }

    // Find next node(s)
    const nextEdges = workflow.edges.filter((edge: Edge) => edge.source === node.id);
    if (nextEdges.length === 0) {
      // No next node, return current result
      return result.output;
    }

    // Handle multiple edges (conditions)
    for (const edge of nextEdges) {
      if (edge.condition) {
        // Evaluate edge condition
        const conditionMet = this.evaluateEdgeCondition(
          edge.condition,
          result.output,
          context.variables,
        );
        if (!conditionMet) continue;
      }

      // Find and execute next node
      const nextNode = workflow.nodes.find((node: Node) => node.id === edge.target);
      if (nextNode) {
        return await this.executeNode(nextNode, workflow, context, result.output);
      }
    }

    // No valid next node found
    return result.output;
  }

  private evaluateEdgeCondition(
    condition: string,
    input: any,
    variables: Record<string, any>,
  ): boolean {
    // Simple condition evaluation for edges
    try {
      const context = { input, ...variables };
      const processedCondition = condition.replace(
        /\{(\w+)\}/g,
        (match, key) => {
          return JSON.stringify(context[key as keyof typeof context] || null);
        },
      );
      return eval(processedCondition);
    } catch {
      return false;
    }
  }

  async resumeExecution(
    executionId: string,
    humanInput: any,
  ): Promise<any> {
    const pausedData = await redis.get(
      `synapse:execution:paused:${executionId}`,
    );
    if (!pausedData) {
      throw new Error("Paused execution not found or expired");
    }

    const { context, nodeId, input } = JSON.parse(pausedData);
    const workflow = this.workflows.get(context.workflowId);
    if (!workflow) {
      throw new Error("Workflow not found");
    }

    // Clean up paused state
    await redis.del(`synapse:execution:paused:${executionId}`);

    // Find next node after human node
    const nextEdges = workflow.edges.filter((edge: Edge) => edge.source === nodeId);
    if (nextEdges.length === 0) {
      return humanInput;
    }

    const nextNode = workflow.nodes.find((node: Node) => node.id === nextEdges[0].target);
    if (!nextNode) {
      return humanInput;
    }

    // Continue execution with human input
    return await this.executeNode(nextNode, workflow, context, humanInput);
  }

  async loadWorkflowsFromRedis(tenantId: string): Promise<void> {
    const keys = await redis.keys(`synapse:workflow:${tenantId}:*`);
    for (const key of keys) {
      const data = await redis.get(key);
      if (data) {
        const workflow = WorkflowSchema.parse(JSON.parse(data));
        this.workflows.set(workflow.id, workflow);
      }
    }
  }
}

export const agentExecutor = new AgentExecutor();
