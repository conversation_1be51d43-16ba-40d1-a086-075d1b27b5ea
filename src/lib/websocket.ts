"use client";

import { io, Socket } from "socket.io-client";

class WebSocketClient {
  private socket: Socket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventHandlers: Map<string, Function[]> = new Map();

  connect(token: string): void {
    this.token = token;

    if (this.socket?.connected) {
      return;
    }

    this.socket = io(process.env.NEXT_PUBLIC_WS_URL || window.location.origin, {
      auth: {
        token: this.token,
      },
      transports: ["websocket", "polling"],
      timeout: 20000,
      autoConnect: false,
    });

    this.setupEventHandlers();

    this.socket.connect();
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on("connect", () => {
      console.log("WebSocket connected");
      this.reconnectAttempts = 0;
      this.emit("connected", { timestamp: Date.now() });
    });

    this.socket.on("disconnect", (reason: any) => {
      console.log("WebSocket disconnected:", reason);
      this.emit("disconnected", { reason, timestamp: Date.now() });

      if (reason === "io server disconnect") {
        // Server initiated disconnect, try to reconnect
        this.reconnect();
      }
    });

    this.socket.on("connect_error", (error: any) => {
      console.error("WebSocket connection error:", error);
      this.emit("error", { error: error.message, timestamp: Date.now() });
      this.reconnect();
    });

    this.socket.on("event", (data: any) => {
      console.log("Received event:", data);
      this.emit("event", data);

      // Emit specific event type
      if (data.type) {
        this.emit(data.type, data);
      }
    });

    this.socket.on("pong", (data: any) => {
      this.emit("pong", data);
    });
  }

  private reconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error("Max reconnection attempts reached");
      this.emit("maxReconnectAttemptsReached", {
        attempts: this.reconnectAttempts,
      });
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(
      `Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`,
    );

    setTimeout(() => {
      if (this.token) {
        this.connect(this.token);
      }
    }, delay);
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.token = null;
    this.reconnectAttempts = 0;
  }

  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  off(event: string, handler?: Function): void {
    if (!this.eventHandlers.has(event)) return;

    if (handler) {
      const handlers = this.eventHandlers.get(event)!;
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      this.eventHandlers.delete(event);
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event) || [];
    handlers.forEach((handler) => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in event handler for ${event}:`, error);
      }
    });
  }

  send(event: string, data: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn("WebSocket not connected, cannot send event:", event);
    }
  }

  ping(): void {
    this.send("ping", { timestamp: Date.now() });
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getConnectionState(): string {
    if (!this.socket) return "disconnected";
    if (this.socket.connected) return "connected";
    return "disconnected";
  }
}

// Export singleton instance
export const wsClient = new WebSocketClient();

// Auto-connect when token is available
if (typeof window !== "undefined") {
  const token = localStorage.getItem("accessToken");
  if (token) {
    wsClient.connect(token);
  }

  // Listen for storage changes to handle login/logout
  window.addEventListener("storage", (e: StorageEvent | any) => {
    if (e.key === "accessToken") {
      if (e.newValue) {
        wsClient.connect(e.newValue);
      } else {
        wsClient.disconnect();
      }
    }
  });
}

export default wsClient;
