"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function DashboardLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();

  useEffect(() => {
    // Check authentication for all dashboard routes
    const token = localStorage.getItem("accessToken");
    if (!token) {
      router.push("/auth");
    }
  }, [router]);

  return <>{children}</>;
}
