"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Activity,
  BarChart3,
  Bot,
  Server,
  Users,
  Zap,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import Link from "next/link";

interface DashboardStats {
  totalAgents: number;
  activeProviders: number;
  totalExecutions: number;
  successRate: number;
  avgResponseTime: number;
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: number;
    status: "success" | "error" | "pending";
  }>;
}

export default function DashboardPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalAgents: 12,
    activeProviders: 3,
    totalExecutions: 1247,
    successRate: 98.5,
    avgResponseTime: 1.2,
    recentActivity: [
      {
        id: "1",
        type: "agent",
        message: "Agent 'Customer Support Bot' executed successfully",
        timestamp: Date.now() - 300000,
        status: "success",
      },
      {
        id: "2",
        type: "provider",
        message: "OpenAI GPT-4 provider status changed to active",
        timestamp: Date.now() - 600000,
        status: "success",
      },
      {
        id: "3",
        type: "system",
        message: "System health check completed",
        timestamp: Date.now() - 900000,
        status: "success",
      },
      {
        id: "4",
        type: "agent",
        message: "Agent 'Data Processor' execution failed",
        timestamp: Date.now() - 1200000,
        status: "error",
      },
    ],
  });

  useEffect(() => {
    // Production-ready authentication check
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        const userData = localStorage.getItem("user");

        if (!token || !userData) {
          console.log("No authentication data found, redirecting to auth");
          router.replace("/auth");
          return;
        }

        // Validate token format
        if (typeof token !== "string" || token.length < 10) {
          console.log("Invalid token format, redirecting to auth");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("user");
          router.replace("/auth");
          return;
        }

        // Parse and validate user data
        let parsedUser;
        try {
          parsedUser = JSON.parse(userData);
          if (!parsedUser || !parsedUser.id || !parsedUser.email) {
            throw new Error("Invalid user data structure");
          }
        } catch (parseError) {
          console.error("Failed to parse user data:", parseError);
          localStorage.removeItem("accessToken");
          localStorage.removeItem("user");
          router.replace("/auth");
          return;
        }

        // Verify token with server
        try {
          const response = await fetch("/api/auth/verify", {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error("Token verification failed");
          }
        } catch (verifyError) {
          console.error("Token verification failed:", verifyError);
          localStorage.removeItem("accessToken");
          localStorage.removeItem("user");
          router.replace("/auth");
          return;
        }

        setUser(parsedUser);
      } catch (error) {
        console.error("Authentication check failed:", error);
        router.replace("/auth");
      }
    };

    checkAuth();
  }, [router]);

  const getActivityIcon = (type: string, status: string) => {
    if (status === "error")
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    if (status === "success")
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <Clock className="h-4 w-4 text-yellow-500" />;
  };

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ago`;
    return `${minutes}m ago`;
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Welcome Section */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Welcome back, {user.email}</h1>
            <p className="text-muted-foreground mt-1">
              Here's what's happening with your AI workflows today.
            </p>
          </div>
          <Badge variant="outline" className="px-3 py-1">
            {user.role}
          </Badge>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Agents
              </CardTitle>
              <Bot className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalAgents}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +2 from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Providers
              </CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeProviders}</div>
              <p className="text-xs text-muted-foreground">
                All systems operational
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Executions
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.totalExecutions.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +12% from last week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Success Rate
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.successRate}%</div>
              <Progress value={stats.successRate} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Get started with common tasks</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild className="w-full justify-start">
                <Link href="/dashboard/agent-builder">
                  <Bot className="mr-2 h-4 w-4" />
                  Create New Agent
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="w-full justify-start"
              >
                <Link href="/dashboard/provider-management">
                  <Server className="mr-2 h-4 w-4" />
                  Manage Providers
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="w-full justify-start"
              >
                <Link href="/dashboard/widget-preview">
                  <Zap className="mr-2 h-4 w-4" />
                  Create Widget
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest events and system updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-3"
                  >
                    {getActivityIcon(activity.type, activity.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {activity.message}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatTimeAgo(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Response Time</CardTitle>
              <CardDescription>
                Average response time across all providers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.avgResponseTime}s</div>
              <div className="mt-4 h-2 bg-muted rounded-full">
                <div
                  className="h-2 bg-primary rounded-full"
                  style={{
                    width: `${Math.min(((2 - stats.avgResponseTime) / 2) * 100, 100)}%`,
                  }}
                />
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Target: &lt; 2.0s
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
              <CardDescription>
                Overall system status and health metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">API Endpoints</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    Healthy
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Database</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    Connected
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">WebSocket</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    Active
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Event System</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    Running
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
