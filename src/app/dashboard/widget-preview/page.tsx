"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import WidgetPreview from "@/components/widgets/WidgetPreview";

export default function WidgetPreviewPage() {
  const router = useRouter();

  useEffect(() => {
    // Production-ready authentication check
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        const userData = localStorage.getItem("user");

        if (!token || !userData) {
          console.log("No authentication data found, redirecting to auth");
          router.replace("/auth");
          return;
        }

        // Validate token format
        if (typeof token !== "string" || token.length < 10) {
          console.log("Invalid token format, redirecting to auth");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("user");
          router.replace("/auth");
          return;
        }

        // Verify token with server
        try {
          const response = await fetch("/api/auth/verify", {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error("Token verification failed");
          }
        } catch (verifyError) {
          console.error("Token verification failed:", verifyError);
          localStorage.removeItem("accessToken");
          localStorage.removeItem("user");
          router.replace("/auth");
          return;
        }
      } catch (error) {
        console.error("Authentication check failed:", error);
        router.replace("/auth");
      }
    };

    checkAuth();
  }, [router]);

  return (
    <DashboardLayout>
      <WidgetPreview />
    </DashboardLayout>
  );
}
