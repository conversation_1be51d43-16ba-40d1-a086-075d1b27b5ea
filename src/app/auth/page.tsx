"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import LoginForm from "@/components/auth/LoginForm";
import RegisterForm from "@/components/auth/RegisterForm";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState("login");
  const router = useRouter();

  useEffect(() => {
    // Check if user is already logged in with production-ready validation
    const checkExistingAuth = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        const userData = localStorage.getItem("user");

        if (token && userData) {
          // Validate token format
          if (typeof token === "string" && token.length > 10) {
            try {
              // Verify token with server
              const response = await fetch("/api/auth/verify", {
                method: "GET",
                headers: {
                  Authorization: `Bear<PERSON> ${token}`,
                  "Content-Type": "application/json",
                },
              });

              if (response.ok) {
                console.log(
                  "User already authenticated, redirecting to dashboard",
                );
                router.replace("/dashboard");
                return;
              }
            } catch (verifyError) {
              console.log("Token verification failed, staying on auth page");
            }
          }

          // Clear invalid tokens
          localStorage.removeItem("accessToken");
          localStorage.removeItem("user");
        }
      } catch (error) {
        console.error("Auth check error:", error);
        // Clear potentially corrupted data
        localStorage.removeItem("accessToken");
        localStorage.removeItem("user");
      }
    };

    checkExistingAuth();
  }, [router]);

  const handleAuthSuccess = (user: any, token: string) => {
    // Production-ready success handling with validation
    try {
      if (!user || !token) {
        console.error("Invalid authentication data received");
        return;
      }

      console.log("Authentication successful, redirecting to dashboard");

      // Force redirect to dashboard with replace to prevent back navigation
      router.replace("/dashboard");

      // Fallback redirect in case router fails
      setTimeout(() => {
        if (window.location.pathname !== "/dashboard") {
          window.location.replace("/dashboard");
        }
      }, 1000);
    } catch (error) {
      console.error("Error handling auth success:", error);
      // Fallback redirect
      window.location.replace("/dashboard");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4">
            <span className="text-2xl font-bold text-primary-foreground">
              S
            </span>
          </div>
          <h1 className="text-3xl font-bold">SynapseAI</h1>
          <p className="text-muted-foreground mt-2">
            AI Orchestration Platform
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="login">Sign In</TabsTrigger>
            <TabsTrigger value="register">Sign Up</TabsTrigger>
          </TabsList>

          <TabsContent value="login">
            <LoginForm
              onSuccess={handleAuthSuccess}
              onRegisterClick={() => setActiveTab("register")}
            />
          </TabsContent>

          <TabsContent value="register">
            <RegisterForm
              onSuccess={handleAuthSuccess}
              onLoginClick={() => setActiveTab("login")}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
