import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowR<PERSON>, Bar<PERSON>hart2, <PERSON>, Settings, Zap } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Hero Section */}
      <header className="container mx-auto py-16 px-4 md:px-6 lg:py-24">
        <div className="flex flex-col items-center text-center space-y-6">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            SynapseAI Orchestration Platform
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Intelligently orchestrate multiple AI providers through a
            click-based interface with real-time, event-driven workflows and
            transparent decision-making.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/dashboard" className="inline-flex items-center">
                Go to Dashboard <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/auth" className="inline-flex items-center">
                Get Started
              </Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="container mx-auto py-16 px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold">Platform Features</h2>
          <p className="text-muted-foreground mt-2">
            Everything you need to build powerful AI workflows
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="bg-card">
            <CardHeader>
              <div className="p-2 w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Zap className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Multi-Provider Intelligence</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Intelligently route between OpenAI, Claude, and Gemini based on
                context and task requirements.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card">
            <CardHeader>
              <div className="p-2 w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Settings className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Visual Agent Builder</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Create complex AI workflows with our intuitive drag-and-drop
                interface. No coding required.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card">
            <CardHeader>
              <div className="p-2 w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <BarChart2 className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Usage Monitoring</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Track AI provider usage, costs, and performance metrics in
                real-time through detailed analytics.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card">
            <CardHeader>
              <div className="p-2 w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <Code className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Developer SDK</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Integrate SynapseAI into your applications with our fully typed
                TypeScript SDK.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Dashboard Preview Section */}
      <section className="container mx-auto py-16 px-4 md:px-6 bg-muted/30 rounded-lg my-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold">Explore the Platform</h2>
          <p className="text-muted-foreground mt-2">
            Get started with our intuitive dashboard
          </p>
        </div>

        <Tabs defaultValue="agent-builder" className="w-full max-w-4xl mx-auto">
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="agent-builder">Agent Builder</TabsTrigger>
            <TabsTrigger value="provider-management">
              Provider Management
            </TabsTrigger>
            <TabsTrigger value="widget-preview">Widget Preview</TabsTrigger>
          </TabsList>
          <TabsContent
            value="agent-builder"
            className="border rounded-lg p-4 bg-card"
          >
            <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
              <img
                src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=800&q=80"
                alt="Agent Builder Interface"
                className="rounded-md w-full h-full object-cover opacity-90"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-md">
                <Button asChild>
                  <Link href="/dashboard/agent-builder">
                    Preview Agent Builder
                  </Link>
                </Button>
              </div>
            </div>
            <div className="mt-4">
              <h3 className="text-xl font-semibold">Visual Workflow Builder</h3>
              <p className="text-muted-foreground mt-2">
                Create, test, and deploy AI agents with our intuitive
                drag-and-drop interface. Connect nodes to build complex
                workflows without writing code.
              </p>
            </div>
          </TabsContent>
          <TabsContent
            value="provider-management"
            className="border rounded-lg p-4 bg-card"
          >
            <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
              <img
                src="https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&q=80"
                alt="Provider Management Interface"
                className="rounded-md w-full h-full object-cover opacity-90"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-md">
                <Button asChild>
                  <Link href="/dashboard/provider-management">
                    Preview Provider Management
                  </Link>
                </Button>
              </div>
            </div>
            <div className="mt-4">
              <h3 className="text-xl font-semibold">AI Provider Management</h3>
              <p className="text-muted-foreground mt-2">
                Configure and manage multiple AI providers, set priorities, and
                track usage metrics. Easily add new providers and API keys
                through a simple interface.
              </p>
            </div>
          </TabsContent>
          <TabsContent
            value="widget-preview"
            className="border rounded-lg p-4 bg-card"
          >
            <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
              <img
                src="https://images.unsplash.com/photo-**********-e076c223a692?w=800&q=80"
                alt="Widget Preview Interface"
                className="rounded-md w-full h-full object-cover opacity-90"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-md">
                <Button asChild>
                  <Link href="/dashboard/widget-preview">Preview Widget</Link>
                </Button>
              </div>
            </div>
            <div className="mt-4">
              <h3 className="text-xl font-semibold">
                Embeddable Widget System
              </h3>
              <p className="text-muted-foreground mt-2">
                Customize and preview your AI widget before embedding it on your
                website. Configure styling, set domain restrictions, and
                generate embed code with ease.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto py-16 px-4 md:px-6 my-8">
        <Card className="bg-primary text-primary-foreground">
          <CardHeader>
            <CardTitle className="text-2xl md:text-3xl">
              Ready to get started?
            </CardTitle>
            <CardDescription className="text-primary-foreground/90">
              Join the next generation of AI orchestration platforms today.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              SynapseAI provides everything you need to build, deploy, and
              manage sophisticated AI workflows.
            </p>
          </CardContent>
          <CardFooter>
            <Button size="lg" variant="secondary" asChild>
              <Link href="/dashboard" className="inline-flex items-center">
                Go to Dashboard
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t mt-auto">
        <div className="container mx-auto py-8 px-4 md:px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="font-bold text-xl">SynapseAI</h3>
              <p className="text-muted-foreground">
                Next-generation AI orchestration platform
              </p>
            </div>
            <div className="flex space-x-4">
              <Link
                href="/dashboard"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Dashboard
              </Link>
              <Link
                href="/auth"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/dashboard/agent-builder"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Agent Builder
              </Link>
            </div>
          </div>
          <div className="mt-8 text-center text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} SynapseAI. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
