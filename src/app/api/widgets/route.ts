import { NextRequest, NextResponse } from "next/server";
import { authService } from "@/lib/auth";
import { redis } from "@/lib/redis";
import { z } from "zod";

const WidgetConfigSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  theme: z.object({
    primaryColor: z.string(),
    backgroundColor: z.string(),
    textColor: z.string(),
    fontFamily: z.string(),
    borderRadius: z.string(),
  }),
  domains: z.array(z.string()),
  agentId: z.string().optional(),
  settings: z.record(z.any()).optional(),
});

async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (!token) {
    throw new Error("No token provided");
  }

  const payload = await authService.verifyToken(token);
  if (!payload) {
    throw new Error("Invalid token");
  }

  return payload;
}

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    const widgetKeys = await redis.keys(`synapse:widget:${user.tenantId}:*`);
    const widgets = [];

    for (const key of widgetKeys) {
      const widgetData = await redis.get(key);
      if (widgetData) {
        widgets.push(JSON.parse(widgetData));
      }
    }

    return NextResponse.json({ widgets });
  } catch (error) {
    console.error("Get widgets error:", error);
    return NextResponse.json(
      { error: "Failed to get widgets" },
      { status: 401 },
    );
  }
}

export async function POST(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[${requestId}] Creating new widget`);

    // Authentication with detailed error handling
    let user;
    try {
      user = await getAuthenticatedUser(request);
    } catch (authError) {
      console.error(`[${requestId}] Authentication failed:`, authError);
      return NextResponse.json(
        {
          error: "Authentication failed",
          code: "AUTH_FAILED",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error(`[${requestId}] JSON parse error:`, parseError);
      return NextResponse.json(
        {
          error: "Invalid JSON in request body",
          code: "INVALID_JSON",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Schema validation
    let widgetConfig;
    try {
      widgetConfig = WidgetConfigSchema.parse(body);
    } catch (validationError) {
      console.error(
        `[${requestId}] Widget validation failed:`,
        validationError,
      );
      return NextResponse.json(
        {
          error: "Widget validation failed",
          code: "VALIDATION_ERROR",
          details:
            validationError instanceof Error
              ? validationError.message
              : String(validationError),
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Additional production validations
    if (!widgetConfig.name || widgetConfig.name.trim().length === 0) {
      return NextResponse.json(
        {
          error: "Widget name cannot be empty",
          code: "INVALID_NAME",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    if (!widgetConfig.theme) {
      return NextResponse.json(
        {
          error: "Widget theme configuration is required",
          code: "MISSING_THEME",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Validate theme colors
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!colorRegex.test(widgetConfig.theme.primaryColor)) {
      return NextResponse.json(
        {
          error: "Invalid primary color format. Must be a valid hex color.",
          code: "INVALID_COLOR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    if (!colorRegex.test(widgetConfig.theme.backgroundColor)) {
      return NextResponse.json(
        {
          error: "Invalid background color format. Must be a valid hex color.",
          code: "INVALID_COLOR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    if (!colorRegex.test(widgetConfig.theme.textColor)) {
      return NextResponse.json(
        {
          error: "Invalid text color format. Must be a valid hex color.",
          code: "INVALID_COLOR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Validate domains
    if (widgetConfig.domains && widgetConfig.domains.length > 0) {
      const domainRegex =
        /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
      for (const domain of widgetConfig.domains) {
        if (!domainRegex.test(domain)) {
          return NextResponse.json(
            {
              error: `Invalid domain format: ${domain}`,
              code: "INVALID_DOMAIN",
              requestId,
              timestamp: Date.now(),
            },
            { status: 400 },
          );
        }
      }
    }

    const widgetId =
      widgetConfig.id ||
      `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const widget = {
      ...widgetConfig,
      id: widgetId,
      tenantId: user.tenantId,
      userId: user.userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      version: "1.0.0",
      metadata: {
        requestId,
        createdBy: user.userId,
        ipAddress: request.headers.get("x-forwarded-for") || "unknown",
      },
    };

    // Save to Redis with error handling
    try {
      await redis.setex(
        `synapse:widget:${user.tenantId}:${widgetId}`,
        30 * 24 * 60 * 60, // 30 days
        JSON.stringify(widget),
      );
    } catch (redisError) {
      console.error(`[${requestId}] Redis save failed:`, redisError);
      return NextResponse.json(
        {
          error: "Failed to save widget configuration",
          code: "STORAGE_ERROR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 500 },
      );
    }

    console.log(`[${requestId}] Widget ${widgetId} created successfully`);

    return NextResponse.json({
      success: true,
      widget,
      requestId,
      timestamp: Date.now(),
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[${requestId}] Unexpected error creating widget:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error: "Internal server error",
        code: "INTERNAL_ERROR",
        details:
          process.env.NODE_ENV === "development"
            ? errorMessage
            : "An unexpected error occurred",
        requestId,
        timestamp: Date.now(),
      },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);
    const body = await request.json();
    const widgetConfig = WidgetConfigSchema.parse(body);

    if (!widgetConfig.id) {
      return NextResponse.json(
        { error: "Widget ID is required for updates" },
        { status: 400 },
      );
    }

    const existingWidget = await redis.get(
      `synapse:widget:${user.tenantId}:${widgetConfig.id}`,
    );

    if (!existingWidget) {
      return NextResponse.json({ error: "Widget not found" }, { status: 404 });
    }

    const widget = {
      ...JSON.parse(existingWidget),
      ...widgetConfig,
      updatedAt: Date.now(),
    };

    await redis.setex(
      `synapse:widget:${user.tenantId}:${widgetConfig.id}`,
      30 * 24 * 60 * 60, // 30 days
      JSON.stringify(widget),
    );

    return NextResponse.json({ widget });
  } catch (error) {
    console.error("Update widget error:", error);
    return NextResponse.json(
      { error: "Failed to update widget" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const widgetId = searchParams.get("id");

    if (!widgetId) {
      return NextResponse.json(
        { error: "Widget ID is required" },
        { status: 400 },
      );
    }

    await redis.del(`synapse:widget:${user.tenantId}:${widgetId}`);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Delete widget error:", error);
    return NextResponse.json(
      { error: "Failed to delete widget" },
      { status: 500 },
    );
  }
}
