import { NextRequest, NextResponse } from "next/server";
import { authService, PERMISSIONS } from "@/lib/auth";
import { agentExecutor, Workflow } from "@/lib/agents";
import { redis } from "@/lib/redis";
import { z } from "zod";

const WorkflowCreateSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  nodes: z.array(z.any()),
  edges: z.array(z.any()),
  settings: z.record(z.any()).optional(),
});

async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (!token) {
    throw new Error("No token provided");
  }

  const payload = await authService.verifyToken(token);
  if (!payload) {
    throw new Error("Invalid token");
  }

  return payload;
}

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (!authService.hasPermission(user.permissions, PERMISSIONS.AGENT_READ)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const workflowKeys = await redis.keys(
      `synapse:workflow:${user.tenantId}:*`,
    );
    const workflows = [];

    for (const key of workflowKeys) {
      const workflowData = await redis.get(key);
      if (workflowData) {
        workflows.push(JSON.parse(workflowData));
      }
    }

    return NextResponse.json({ workflows });
  } catch (error) {
    console.error("Get workflows error:", error);
    return NextResponse.json(
      { error: "Failed to get workflows" },
      { status: 401 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.AGENT_CREATE)
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const workflowData = WorkflowCreateSchema.parse(body);

    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const workflow: Workflow = {
      ...workflowData,
      id: workflowId,
      version: 1,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    // Save to Redis
    await redis.setex(
      `synapse:workflow:${user.tenantId}:${workflowId}`,
      30 * 24 * 60 * 60, // 30 days
      JSON.stringify(workflow),
    );

    // Register with agent executor
    await agentExecutor.registerWorkflow(workflow);

    return NextResponse.json({ workflow });
  } catch (error) {
    console.error("Create workflow error:", error);
    return NextResponse.json(
      { error: "Failed to create workflow" },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.AGENT_UPDATE)
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Workflow ID is required" },
        { status: 400 },
      );
    }

    const existingWorkflow = await redis.get(
      `synapse:workflow:${user.tenantId}:${id}`,
    );

    if (!existingWorkflow) {
      return NextResponse.json(
        { error: "Workflow not found" },
        { status: 404 },
      );
    }

    const workflow = {
      ...JSON.parse(existingWorkflow),
      ...updateData,
      updatedAt: Date.now(),
      version: JSON.parse(existingWorkflow).version + 1,
    };

    await redis.setex(
      `synapse:workflow:${user.tenantId}:${id}`,
      30 * 24 * 60 * 60, // 30 days
      JSON.stringify(workflow),
    );

    // Re-register with agent executor
    await agentExecutor.registerWorkflow(workflow);

    return NextResponse.json({ workflow });
  } catch (error) {
    console.error("Update workflow error:", error);
    return NextResponse.json(
      { error: "Failed to update workflow" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.AGENT_DELETE)
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get("id");

    if (!workflowId) {
      return NextResponse.json(
        { error: "Workflow ID is required" },
        { status: 400 },
      );
    }

    await redis.del(`synapse:workflow:${user.tenantId}:${workflowId}`);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Delete workflow error:", error);
    return NextResponse.json(
      { error: "Failed to delete workflow" },
      { status: 500 },
    );
  }
}
