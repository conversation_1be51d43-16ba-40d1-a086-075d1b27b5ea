import { NextRequest, NextResponse } from "next/server";
import { authService, PERMISSIONS } from "@/lib/auth";
import { agentExecutor } from "@/lib/agents";
import { memoryEngine } from "@/lib/redis";
import { z } from "zod";

const ExecuteRequestSchema = z.object({
  workflowId: z.string(),
  input: z.record(z.any()),
  sessionId: z.string().optional(),
});

async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (!token) {
    throw new Error("No token provided");
  }

  const payload = await authService.verifyToken(token);
  if (!payload) {
    throw new Error("Invalid token");
  }

  return payload;
}

export async function POST(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const startTime = Date.now();

  try {
    console.log(`[${requestId}] Starting workflow execution request`);

    // Authentication with detailed error handling
    let user;
    try {
      user = await getAuthenticatedUser(request);
    } catch (authError) {
      console.error(`[${requestId}] Authentication failed:`, authError);
      return NextResponse.json(
        {
          error: "Authentication failed",
          code: "AUTH_FAILED",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Permission check with detailed logging
    if (!authService.hasPermission(user.permissions, PERMISSIONS.AGENT_READ)) {
      console.warn(`[${requestId}] Permission denied for user ${user.userId}`);
      return NextResponse.json(
        {
          error: "Insufficient permissions",
          code: "PERMISSION_DENIED",
          required: PERMISSIONS.AGENT_READ,
          userPermissions: user.permissions,
          requestId,
          timestamp: Date.now(),
        },
        { status: 403 },
      );
    }

    // Request body validation with comprehensive error handling
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error(`[${requestId}] JSON parse error:`, parseError);
      return NextResponse.json(
        {
          error: "Invalid JSON in request body",
          code: "INVALID_JSON",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Schema validation with detailed error reporting
    let validatedData;
    try {
      validatedData = ExecuteRequestSchema.parse(body);
    } catch (validationError) {
      console.error(
        `[${requestId}] Request validation failed:`,
        validationError,
      );
      return NextResponse.json(
        {
          error: "Request validation failed",
          code: "VALIDATION_ERROR",
          details:
            validationError instanceof Error
              ? validationError.message
              : String(validationError),
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    const { workflowId, input, sessionId } = validatedData;

    // Additional input validation
    if (!workflowId || workflowId.trim().length === 0) {
      return NextResponse.json(
        {
          error: "Workflow ID cannot be empty",
          code: "INVALID_WORKFLOW_ID",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    if (!input || typeof input !== "object") {
      return NextResponse.json(
        {
          error: "Input must be a valid object",
          code: "INVALID_INPUT",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    console.log(
      `[${requestId}] Executing workflow ${workflowId} for user ${user.userId}`,
    );

    // Create or get session with error handling
    let actualSessionId;
    try {
      actualSessionId =
        sessionId || (await memoryEngine.createSession(user.userId, input));
    } catch (sessionError) {
      console.error(`[${requestId}] Session creation failed:`, sessionError);
      return NextResponse.json(
        {
          error: "Failed to create session",
          code: "SESSION_ERROR",
          details:
            sessionError instanceof Error
              ? sessionError.message
              : String(sessionError),
          requestId,
          timestamp: Date.now(),
        },
        { status: 500 },
      );
    }

    // Execute workflow with comprehensive error handling
    let result;
    try {
      result = await agentExecutor.executeWorkflow(
        workflowId,
        input,
        actualSessionId,
        user.userId,
        user.tenantId,
      );
    } catch (executionError) {
      const errorMessage =
        executionError instanceof Error
          ? executionError.message
          : String(executionError);
      console.error(`[${requestId}] Workflow execution failed:`, {
        workflowId,
        userId: user.userId,
        tenantId: user.tenantId,
        error: errorMessage,
        executionTime: Date.now() - startTime,
      });

      return NextResponse.json(
        {
          error: "Workflow execution failed",
          code: "EXECUTION_ERROR",
          details: errorMessage,
          workflowId,
          sessionId: actualSessionId,
          requestId,
          executionTime: Date.now() - startTime,
          timestamp: Date.now(),
        },
        { status: 500 },
      );
    }

    const executionTime = Date.now() - startTime;
    console.log(
      `[${requestId}] Workflow execution completed successfully in ${executionTime}ms`,
    );

    // Validate result before returning
    if (result === undefined || result === null) {
      console.warn(`[${requestId}] Workflow returned null/undefined result`);
      result = {
        message: "Workflow completed with no result",
        timestamp: Date.now(),
      };
    }

    return NextResponse.json({
      success: true,
      result,
      sessionId: actualSessionId,
      workflowId,
      executedAt: Date.now(),
      executionTime,
      requestId,
      metadata: {
        userId: user.userId,
        tenantId: user.tenantId,
        version: "1.0.0",
      },
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const executionTime = Date.now() - startTime;

    console.error(`[${requestId}] Unexpected error in workflow execution:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      executionTime,
    });

    return NextResponse.json(
      {
        error: "Internal server error",
        code: "INTERNAL_ERROR",
        details:
          process.env.NODE_ENV === "development"
            ? errorMessage
            : "An unexpected error occurred",
        requestId,
        executionTime,
        timestamp: Date.now(),
      },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (!authService.hasPermission(user.permissions, PERMISSIONS.AGENT_READ)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const activeExecutions = agentExecutor.getActiveExecutions();
    const userExecutions = activeExecutions.filter(
      (execution) => execution.tenantId === user.tenantId,
    );

    return NextResponse.json({ executions: userExecutions });
  } catch (error) {
    console.error("Get executions error:", error);
    return NextResponse.json(
      { error: "Failed to get executions" },
      { status: 500 },
    );
  }
}
