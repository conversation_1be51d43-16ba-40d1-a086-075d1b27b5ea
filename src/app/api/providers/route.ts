import { NextRequest, NextResponse } from "next/server";
import { authService, PERMISSIONS } from "@/lib/auth";
import { providerSelector, ProviderConfig } from "@/lib/providers";
import { redis } from "@/lib/redis";
import { z } from "zod";

const ProviderCreateSchema = z.object({
  name: z.string(),
  type: z.enum(["openai", "anthropic", "google", "custom"]),
  apiKey: z.string(),
  baseUrl: z.string().optional(),
  priority: z.number().min(1),
  costPerToken: z.number().min(0).optional(),
  maxTokens: z.number().optional(),
  timeout: z.number().default(30000),
  retries: z.number().default(3),
  capabilities: z.array(z.string()).default([]),
});

async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (!token) {
    throw new Error("No token provided");
  }

  const payload = await authService.verifyToken(token);
  if (!payload) {
    throw new Error("Invalid token");
  }

  return payload;
}

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.PROVIDER_READ)
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const providerKeys = await redis.keys(
      `synapse:provider:${user.tenantId}:*`,
    );
    const providers = [];

    for (const key of providerKeys) {
      const providerData = await redis.get(key);
      if (providerData) {
        const provider = JSON.parse(providerData);
        // Don't return API keys in list view
        providers.push({ ...provider, apiKey: "***" });
      }
    }

    // Also get performance metrics
    const metrics = providerSelector.getAllMetrics();

    return NextResponse.json({ providers, metrics });
  } catch (error) {
    console.error("Get providers error:", error);
    return NextResponse.json(
      { error: "Failed to get providers" },
      { status: 401 },
    );
  }
}

export async function POST(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[${requestId}] Creating new provider`);

    // Authentication with detailed error handling
    let user;
    try {
      user = await getAuthenticatedUser(request);
    } catch (authError) {
      console.error(`[${requestId}] Authentication failed:`, authError);
      return NextResponse.json(
        {
          error: "Authentication failed",
          code: "AUTH_FAILED",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Permission check
    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.PROVIDER_CREATE)
    ) {
      console.warn(`[${requestId}] Permission denied for user ${user.userId}`);
      return NextResponse.json(
        {
          error: "Insufficient permissions",
          code: "PERMISSION_DENIED",
          required: PERMISSIONS.PROVIDER_CREATE,
          requestId,
          timestamp: Date.now(),
        },
        { status: 403 },
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error(`[${requestId}] JSON parse error:`, parseError);
      return NextResponse.json(
        {
          error: "Invalid JSON in request body",
          code: "INVALID_JSON",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Schema validation
    let providerData;
    try {
      providerData = ProviderCreateSchema.parse(body);
    } catch (validationError) {
      console.error(
        `[${requestId}] Provider validation failed:`,
        validationError,
      );
      return NextResponse.json(
        {
          error: "Provider validation failed",
          code: "VALIDATION_ERROR",
          details:
            validationError instanceof Error
              ? validationError.message
              : String(validationError),
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Additional production validations
    if (!providerData.name || providerData.name.trim().length === 0) {
      return NextResponse.json(
        {
          error: "Provider name cannot be empty",
          code: "INVALID_NAME",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    if (!providerData.apiKey || providerData.apiKey.trim().length === 0) {
      return NextResponse.json(
        {
          error: "API key cannot be empty",
          code: "INVALID_API_KEY",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    if (providerData.apiKey.length < 10) {
      return NextResponse.json(
        {
          error: "API key appears to be too short",
          code: "INVALID_API_KEY_LENGTH",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    const providerId = `provider_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const provider: ProviderConfig = {
      ...providerData,
      id: providerId,
      status: "active",
      costPerToken: providerData.costPerToken || 0.00005,
      timeout: providerData.timeout || 30000,
      retries: providerData.retries || 3,
      capabilities: providerData.capabilities || [],
      metadata: {
        tenantId: user.tenantId,
        createdBy: user.userId,
        createdAt: Date.now(),
        version: "1.0.0",
        requestId,
      },
    };

    // Validate provider configuration
    try {
      // Test API key format based on provider type
      validateApiKeyFormat(provider.type, provider.apiKey);
    } catch (keyError) {
      console.error(`[${requestId}] API key validation failed:`, keyError);
      return NextResponse.json(
        {
          error: "Invalid API key format",
          code: "INVALID_API_KEY_FORMAT",
          details:
            keyError instanceof Error ? keyError.message : String(keyError),
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Save to Redis with error handling
    try {
      await redis.setex(
        `synapse:provider:${user.tenantId}:${providerId}`,
        30 * 24 * 60 * 60, // 30 days
        JSON.stringify(provider),
      );
    } catch (redisError) {
      console.error(`[${requestId}] Redis save failed:`, redisError);
      return NextResponse.json(
        {
          error: "Failed to save provider configuration",
          code: "STORAGE_ERROR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 500 },
      );
    }

    // Register with provider selector
    try {
      providerSelector.addProvider(provider);
    } catch (selectorError) {
      console.error(
        `[${requestId}] Provider selector registration failed:`,
        selectorError,
      );
      // Don't fail the request, but log the error
      console.warn(
        `Provider ${providerId} saved but not registered with selector`,
      );
    }

    console.log(`[${requestId}] Provider ${providerId} created successfully`);

    return NextResponse.json({
      success: true,
      provider: {
        ...provider,
        apiKey: "***", // Don't return API key
      },
      requestId,
      timestamp: Date.now(),
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[${requestId}] Unexpected error creating provider:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error: "Internal server error",
        code: "INTERNAL_ERROR",
        details:
          process.env.NODE_ENV === "development"
            ? errorMessage
            : "An unexpected error occurred",
        requestId,
        timestamp: Date.now(),
      },
      { status: 500 },
    );
  }
}

function validateApiKeyFormat(type: string, apiKey: string): void {
  switch (type) {
    case "openai":
      if (!apiKey.startsWith("sk-")) {
        throw new Error("OpenAI API keys must start with 'sk-'");
      }
      break;
    case "anthropic":
      if (!apiKey.startsWith("sk-ant-")) {
        throw new Error("Anthropic API keys must start with 'sk-ant-'");
      }
      break;
    case "google":
      if (!apiKey.startsWith("AIza")) {
        throw new Error("Google API keys must start with 'AIza'");
      }
      break;
    case "custom":
      // Custom providers can have any format
      break;
    default:
      throw new Error(`Unknown provider type: ${type}`);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.PROVIDER_UPDATE)
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Provider ID is required" },
        { status: 400 },
      );
    }

    const existingProvider = await redis.get(
      `synapse:provider:${user.tenantId}:${id}`,
    );

    if (!existingProvider) {
      return NextResponse.json(
        { error: "Provider not found" },
        { status: 404 },
      );
    }

    const provider = {
      ...JSON.parse(existingProvider),
      ...updateData,
      metadata: {
        ...JSON.parse(existingProvider).metadata,
        updatedAt: Date.now(),
        updatedBy: user.userId,
      },
    };

    await redis.setex(
      `synapse:provider:${user.tenantId}:${id}`,
      30 * 24 * 60 * 60, // 30 days
      JSON.stringify(provider),
    );

    // Update provider selector
    providerSelector.addProvider(provider);

    return NextResponse.json({
      provider: { ...provider, apiKey: "***" }, // Don't return API key
    });
  } catch (error) {
    console.error("Update provider error:", error);
    return NextResponse.json(
      { error: "Failed to update provider" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    if (
      !authService.hasPermission(user.permissions, PERMISSIONS.PROVIDER_DELETE)
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const providerId = searchParams.get("id");

    if (!providerId) {
      return NextResponse.json(
        { error: "Provider ID is required" },
        { status: 400 },
      );
    }

    await redis.del(`synapse:provider:${user.tenantId}:${providerId}`);
    providerSelector.removeProvider(providerId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Delete provider error:", error);
    return NextResponse.json(
      { error: "Failed to delete provider" },
      { status: 500 },
    );
  }
}
