import { NextRequest, NextResponse } from "next/server";
import { authService } from "@/lib/auth";
import { z } from "zod";

const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  role: z.enum(["admin", "developer", "user"]).default("user"),
  tenantId: z.string().default("default"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const userData = RegisterSchema.parse(body);

    // Check if user already exists
    const existingUser = await authService.getUserByEmail(userData.email);
    if (existingUser) {
      return NextResponse.json(
        { error: "User already exists" },
        { status: 409 },
      );
    }

    const user = await authService.createUser({
      ...userData,
      isActive: true,
    });

    const tokens = authService.generateTokens(user);

    const response = NextResponse.json({
      user: { ...user, password: undefined },
      accessToken: tokens.accessToken,
    });

    // Set refresh token as httpOnly cookie
    response.cookies.set("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return response;
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json({ error: "Registration failed" }, { status: 500 });
  }
}
