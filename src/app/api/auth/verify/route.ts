import { NextRequest, NextResponse } from "next/server";
import { authService } from "@/lib/auth";

export async function GET(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[${requestId}] Token verification request`);

    const authHeader = request.headers.get("authorization");
    const token = authHeader?.replace("Bearer ", "");

    if (!token) {
      console.log(`[${requestId}] No token provided`);
      return NextResponse.json(
        {
          error: "No token provided",
          code: "NO_TOKEN",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Validate token format
    if (typeof token !== "string" || token.length < 10) {
      console.log(`[${requestId}] Invalid token format`);
      return NextResponse.json(
        {
          error: "Invalid token format",
          code: "INVALID_TOKEN_FORMAT",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Verify token with auth service
    const payload = await authService.verifyToken(token);

    if (!payload) {
      console.log(`[${requestId}] Token verification failed`);
      return NextResponse.json(
        {
          error: "Invalid or expired token",
          code: "TOKEN_INVALID",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Get user data to ensure user still exists and is active
    const user = await authService.getUserById(payload.userId);

    if (!user || !user.isActive) {
      console.log(`[${requestId}] User not found or inactive`);
      return NextResponse.json(
        {
          error: "User not found or inactive",
          code: "USER_INACTIVE",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    console.log(
      `[${requestId}] Token verification successful for user ${payload.userId}`,
    );

    return NextResponse.json({
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId,
        permissions: user.permissions,
      },
      requestId,
      timestamp: Date.now(),
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[${requestId}] Token verification error:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error: "Token verification failed",
        code: "VERIFICATION_ERROR",
        details:
          process.env.NODE_ENV === "development"
            ? errorMessage
            : "An unexpected error occurred",
        requestId,
        timestamp: Date.now(),
      },
      { status: 500 },
    );
  }
}
