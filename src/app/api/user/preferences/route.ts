import { NextRequest, NextResponse } from "next/server";
import { authService } from "@/lib/auth";
import { redis } from "@/lib/redis";
import { z } from "zod";

const PreferencesSchema = z.object({
  theme: z.enum(["light", "dark", "system"]).optional(),
  language: z.string().optional(),
  notifications: z
    .object({
      email: z.boolean().optional(),
      push: z.boolean().optional(),
      desktop: z.boolean().optional(),
    })
    .optional(),
  dashboard: z
    .object({
      layout: z.string().optional(),
      widgets: z.array(z.string()).optional(),
    })
    .optional(),
});

async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (!token) {
    throw new Error("No token provided");
  }

  const payload = await authService.verifyToken(token);
  if (!payload) {
    throw new Error("Invalid token");
  }

  return payload;
}

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    const preferences = await redis.get(`synapse:preferences:${user.userId}`);

    return NextResponse.json({
      preferences: preferences ? JSON.parse(preferences) : {},
    });
  } catch (error) {
    console.error("Get preferences error:", error);
    return NextResponse.json(
      { error: "Failed to get preferences" },
      { status: 401 },
    );
  }
}

export async function PUT(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[${requestId}] Updating user preferences`);

    // Authentication with detailed error handling
    let user;
    try {
      user = await getAuthenticatedUser(request);
    } catch (authError) {
      console.error(`[${requestId}] Authentication failed:`, authError);
      return NextResponse.json(
        {
          error: "Authentication failed",
          code: "AUTH_FAILED",
          requestId,
          timestamp: Date.now(),
        },
        { status: 401 },
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error(`[${requestId}] JSON parse error:`, parseError);
      return NextResponse.json(
        {
          error: "Invalid JSON in request body",
          code: "INVALID_JSON",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Schema validation
    let preferences;
    try {
      preferences = PreferencesSchema.parse(body);
    } catch (validationError) {
      console.error(
        `[${requestId}] Preferences validation failed:`,
        validationError,
      );
      return NextResponse.json(
        {
          error: "Preferences validation failed",
          code: "VALIDATION_ERROR",
          details:
            validationError instanceof Error
              ? validationError.message
              : String(validationError),
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Get existing preferences with error handling
    let existingPrefs;
    try {
      const existingData = await redis.get(
        `synapse:preferences:${user.userId}`,
      );
      existingPrefs = existingData ? JSON.parse(existingData) : {};
    } catch (redisError) {
      console.error(
        `[${requestId}] Failed to retrieve existing preferences:`,
        redisError,
      );
      existingPrefs = {}; // Continue with empty preferences
    }

    // Validate existing preferences structure
    if (typeof existingPrefs !== "object" || existingPrefs === null) {
      console.warn(
        `[${requestId}] Invalid existing preferences structure, resetting`,
      );
      existingPrefs = {};
    }

    // Merge with new preferences
    const updatedPrefs = {
      ...existingPrefs,
      ...preferences,
      updatedAt: Date.now(),
      version: "1.0.0",
      metadata: {
        requestId,
        updatedBy: user.userId,
        ipAddress: request.headers.get("x-forwarded-for") || "unknown",
      },
    };

    // Validate final preferences structure
    try {
      JSON.stringify(updatedPrefs);
    } catch (serializationError) {
      console.error(
        `[${requestId}] Preferences serialization failed:`,
        serializationError,
      );
      return NextResponse.json(
        {
          error: "Preferences contain non-serializable data",
          code: "SERIALIZATION_ERROR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 400 },
      );
    }

    // Save to Redis with error handling
    try {
      await redis.setex(
        `synapse:preferences:${user.userId}`,
        30 * 24 * 60 * 60, // 30 days
        JSON.stringify(updatedPrefs),
      );
    } catch (redisError) {
      console.error(`[${requestId}] Redis save failed:`, redisError);
      return NextResponse.json(
        {
          error: "Failed to save preferences",
          code: "STORAGE_ERROR",
          requestId,
          timestamp: Date.now(),
        },
        { status: 500 },
      );
    }

    console.log(
      `[${requestId}] User preferences updated successfully for user ${user.userId}`,
    );

    return NextResponse.json({
      success: true,
      preferences: updatedPrefs,
      requestId,
      timestamp: Date.now(),
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[${requestId}] Unexpected error updating preferences:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error: "Internal server error",
        code: "INTERNAL_ERROR",
        details:
          process.env.NODE_ENV === "development"
            ? errorMessage
            : "An unexpected error occurred",
        requestId,
        timestamp: Date.now(),
      },
      { status: 500 },
    );
  }
}
