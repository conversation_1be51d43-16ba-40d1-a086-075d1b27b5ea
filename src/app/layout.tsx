import { TempoInit } from "@/components/tempo-init";
// import { ThemeProvider } from "next-themes";
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import Script from "next/script";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "SynapseAI - AI Orchestration Platform",
  description:
    "Next-generation AI orchestration platform with real-time workflows",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <Script src="https://api.tempo.new/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js" />
      <body className={inter.className}>
        {/* <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        > */}
          {children}
          <TempoInit />
        {/* </ThemeProvider> */}
      </body>
    </html>
  );
}
