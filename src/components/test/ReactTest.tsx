"use client";

import React, { createContext, useContext } from "react";

// Test if createContext works
const TestContext = createContext<string>("test");

export default function ReactTest() {
  const value = useContext(TestContext);
  
  return (
    <TestContext.Provider value="React is working!">
      <div>
        <h1>React Test</h1>
        <p>Context value: {value}</p>
      </div>
    </TestContext.Provider>
  );
}
