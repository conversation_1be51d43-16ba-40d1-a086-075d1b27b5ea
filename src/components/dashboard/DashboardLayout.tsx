"use client";

import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import {
  Bell,
  Settings,
  LogOut,
  Home,
  Code,
  Workflow,
  Server,
  BarChart3,
  Users,
} from "lucide-react";

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

export default function DashboardLayout({
  children = <div className="p-6">Dashboard Content</div>,
}: DashboardLayoutProps) {
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState("overview");

  const navigation = [
    {
      name: "Overview",
      href: "/dashboard",
      icon: Home,
      current: pathname === "/dashboard",
    },
    {
      name: "Agent Builder",
      href: "/dashboard/agent-builder",
      icon: Workflow,
      current: pathname === "/dashboard/agent-builder",
    },
    {
      name: "Provider Management",
      href: "/dashboard/provider-management",
      icon: Server,
      current: pathname === "/dashboard/provider-management",
    },
    {
      name: "Analytics",
      href: "/dashboard/analytics",
      icon: BarChart3,
      current: pathname === "/dashboard/analytics",
    },
    {
      name: "Widget Preview",
      href: "/dashboard/widget-preview",
      icon: Code,
      current: pathname === "/dashboard/widget-preview",
    },
    {
      name: "Team",
      href: "/dashboard/team",
      icon: Users,
      current: pathname === "/dashboard/team",
    },
  ];

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 border-r bg-card">
        <div className="flex h-16 items-center px-4">
          <div className="flex items-center">
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
              S
            </div>
            <span className="ml-2 text-xl font-semibold">SynapseAI</span>
          </div>
        </div>
        <Separator />
        <nav className="space-y-1 p-2">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${item.current ? "bg-accent text-accent-foreground" : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"}`}
            >
              <item.icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          ))}
        </nav>
        <div className="absolute bottom-0 w-64 p-4 border-t">
          <div className="flex items-center">
            <Avatar>
              <AvatarImage src="https://api.dicebear.com/7.x/avataaars/svg?seed=admin" />
              <AvatarFallback>AD</AvatarFallback>
            </Avatar>
            <div className="ml-3">
              <p className="text-sm font-medium">Admin User</p>
              <p className="text-xs text-muted-foreground">
                <EMAIL>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="h-16 border-b bg-card flex items-center justify-between px-6">
          <h1 className="text-xl font-semibold">
            {navigation.find((item) => item.current)?.name || "Dashboard"}
          </h1>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <Settings className="h-5 w-5" />
            </Button>
            <Separator orientation="vertical" className="h-8" />
            <Button
              variant="ghost"
              size="icon"
              onClick={async () => {
                try {
                  const token = localStorage.getItem("accessToken");
                  if (token) {
                    await fetch("/api/auth/logout", {
                      method: "POST",
                      headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json",
                      },
                    });
                  }
                } catch (error) {
                  console.error("Logout error:", error);
                } finally {
                  // Clear all authentication data
                  localStorage.removeItem("accessToken");
                  localStorage.removeItem("user");
                  localStorage.removeItem("refreshToken");

                  // Clear any session storage
                  sessionStorage.clear();

                  // Force redirect to auth page
                  window.location.replace("/auth");
                }
              }}
              title="Logout"
            >
              <LogOut className="h-5 w-5" />
            </Button>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 overflow-auto bg-muted/20 p-6">
          <Card className="bg-background min-h-[calc(100vh-7rem)]">
            {children}
          </Card>
        </main>
      </div>
    </div>
  );
}
