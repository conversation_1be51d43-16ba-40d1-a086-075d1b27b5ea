"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  Edit,
  Check,
  X,
  <PERSON>ert<PERSON><PERSON>cle,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface Provider {
  id: string;
  name: string;
  type: string;
  apiKey: string;
  status: "active" | "inactive" | "error";
  priority: number;
  costPerToken: number;
  responseTime: number;
  successRate: number;
  usageCount: number;
}

const ProviderManagement = () => {
  const [providers, setProviders] = useState<Provider[]>([
    {
      id: "1",
      name: "OpenAI GPT-4",
      type: "openai",
      apiKey: "sk-***************************",
      status: "active",
      priority: 1,
      costPerToken: 0.00006,
      responseTime: 1.2,
      successRate: 99.8,
      usageCount: 12453,
    },
    {
      id: "2",
      name: "Anthropic Claude 3",
      type: "anthropic",
      apiKey: "sk-ant-***************************",
      status: "active",
      priority: 2,
      costPerToken: 0.00008,
      responseTime: 0.9,
      successRate: 99.5,
      usageCount: 8721,
    },
    {
      id: "3",
      name: "Google Gemini Pro",
      type: "google",
      apiKey: "AIza***************************",
      status: "error",
      priority: 3,
      costPerToken: 0.00004,
      responseTime: 1.5,
      successRate: 97.2,
      usageCount: 5432,
    },
  ]);

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentProvider, setCurrentProvider] = useState<Provider | null>(null);
  const [newProvider, setNewProvider] = useState({
    name: "",
    type: "openai",
    apiKey: "",
    priority: 1,
  });

  const handleAddProvider = () => {
    const provider: Provider = {
      id: Date.now().toString(),
      name: newProvider.name,
      type: newProvider.type,
      apiKey: newProvider.apiKey,
      status: "active",
      priority: newProvider.priority,
      costPerToken: 0.00005,
      responseTime: 1.0,
      successRate: 99.0,
      usageCount: 0,
    };

    setProviders([...providers, provider]);
    setNewProvider({
      name: "",
      type: "openai",
      apiKey: "",
      priority: providers.length + 1,
    });
    setIsAddDialogOpen(false);
  };

  const handleEditProvider = () => {
    if (!currentProvider) return;

    const updatedProviders = providers.map((p) =>
      p.id === currentProvider.id ? currentProvider : p,
    );

    setProviders(updatedProviders);
    setIsEditDialogOpen(false);
    setCurrentProvider(null);
  };

  const handleDeleteProvider = (id: string) => {
    setProviders(providers.filter((p) => p.id !== id));
  };

  const handleToggleStatus = (id: string) => {
    setProviders(
      providers.map((p) => {
        if (p.id === id) {
          return {
            ...p,
            status: p.status === "active" ? "inactive" : "active",
          };
        }
        return p;
      }),
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "inactive":
        return <Badge variant="outline">Inactive</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getProviderIcon = (type: string) => {
    switch (type) {
      case "openai":
        return "🔵";
      case "anthropic":
        return "🟣";
      case "google":
        return "🟢";
      default:
        return "🔹";
    }
  };

  return (
    <div className="bg-background p-6 h-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">AI Provider Management</h1>
          <p className="text-muted-foreground">
            Configure and manage your AI service providers
          </p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add Provider
        </Button>
      </div>

      <Tabs defaultValue="providers" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="settings">Global Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          {providers.some((p) => p.status === "error") && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Provider Error</AlertTitle>
              <AlertDescription>
                One or more providers are reporting errors. Please check your
                API keys and provider status.
              </AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Configured Providers</CardTitle>
              <CardDescription>
                Manage your AI providers and their configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Provider</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>API Key</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {providers.map((provider) => (
                    <TableRow key={provider.id}>
                      <TableCell className="font-medium">
                        {provider.name}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span>{getProviderIcon(provider.type)}</span>
                          <span className="capitalize">{provider.type}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {provider.apiKey}
                      </TableCell>
                      <TableCell>{provider.priority}</TableCell>
                      <TableCell>{getStatusBadge(provider.status)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleStatus(provider.id)}
                          >
                            {provider.status === "active"
                              ? "Disable"
                              : "Enable"}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setCurrentProvider(provider);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteProvider(provider.id)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Provider Performance Metrics</CardTitle>
              <CardDescription>
                Compare performance across your AI providers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Provider</TableHead>
                    <TableHead>Cost/Token</TableHead>
                    <TableHead>Avg Response Time</TableHead>
                    <TableHead>Success Rate</TableHead>
                    <TableHead>Usage Count</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {providers.map((provider) => (
                    <TableRow key={provider.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <span>{getProviderIcon(provider.type)}</span>
                          {provider.name}
                        </div>
                      </TableCell>
                      <TableCell>${provider.costPerToken.toFixed(5)}</TableCell>
                      <TableCell>{provider.responseTime}s</TableCell>
                      <TableCell>{provider.successRate}%</TableCell>
                      <TableCell>
                        {provider.usageCount.toLocaleString()}
                      </TableCell>
                      <TableCell>{getStatusBadge(provider.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                <BarChart className="mr-2 h-4 w-4" /> View Detailed Analytics
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Global Provider Settings</CardTitle>
              <CardDescription>
                Configure global settings for all providers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-fallback">Automatic Fallback</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically try the next provider if the current one fails
                  </p>
                </div>
                <Switch id="auto-fallback" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="smart-routing">Smart Routing</Label>
                  <p className="text-sm text-muted-foreground">
                    Route requests to the best provider based on content and
                    performance
                  </p>
                </div>
                <Switch id="smart-routing" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="cost-optimization">Cost Optimization</Label>
                  <p className="text-sm text-muted-foreground">
                    Prefer lower-cost providers when possible
                  </p>
                </div>
                <Switch id="cost-optimization" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout">Request Timeout (seconds)</Label>
                <Input id="timeout" type="number" defaultValue="30" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-retries">Maximum Retries</Label>
                <Input id="max-retries" type="number" defaultValue="3" />
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Provider Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Provider</DialogTitle>
            <DialogDescription>
              Configure a new AI provider for your platform
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newProvider.name}
                onChange={(e) =>
                  setNewProvider({ ...newProvider, name: e.target.value })
                }
                className="col-span-3"
                placeholder="e.g. OpenAI GPT-4"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Provider Type
              </Label>
              <Select
                value={newProvider.type}
                onValueChange={(value) =>
                  setNewProvider({ ...newProvider, type: value })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select provider type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="anthropic">Anthropic</SelectItem>
                  <SelectItem value="google">Google</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="api-key" className="text-right">
                API Key
              </Label>
              <Input
                id="api-key"
                value={newProvider.apiKey}
                onChange={(e) =>
                  setNewProvider({ ...newProvider, apiKey: e.target.value })
                }
                className="col-span-3"
                type="password"
                placeholder="Enter your API key"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                Priority
              </Label>
              <Input
                id="priority"
                value={newProvider.priority}
                onChange={(e) =>
                  setNewProvider({
                    ...newProvider,
                    priority: parseInt(e.target.value) || 1,
                  })
                }
                className="col-span-3"
                type="number"
                min="1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleAddProvider}
              disabled={!newProvider.name || !newProvider.apiKey}
            >
              Add Provider
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Provider Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Provider</DialogTitle>
            <DialogDescription>
              Update your AI provider configuration
            </DialogDescription>
          </DialogHeader>
          {currentProvider && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-name"
                  value={currentProvider.name}
                  onChange={(e) =>
                    setCurrentProvider({
                      ...currentProvider,
                      name: e.target.value,
                    })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-type" className="text-right">
                  Provider Type
                </Label>
                <Select
                  value={currentProvider.type}
                  onValueChange={(value) =>
                    setCurrentProvider({ ...currentProvider, type: value })
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="google">Google</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-api-key" className="text-right">
                  API Key
                </Label>
                <Input
                  id="edit-api-key"
                  value={currentProvider.apiKey}
                  onChange={(e) =>
                    setCurrentProvider({
                      ...currentProvider,
                      apiKey: e.target.value,
                    })
                  }
                  className="col-span-3"
                  type="password"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-priority" className="text-right">
                  Priority
                </Label>
                <Input
                  id="edit-priority"
                  value={currentProvider.priority}
                  onChange={(e) =>
                    setCurrentProvider({
                      ...currentProvider,
                      priority: parseInt(e.target.value) || 1,
                    })
                  }
                  className="col-span-3"
                  type="number"
                  min="1"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-status" className="text-right">
                  Status
                </Label>
                <Select
                  value={currentProvider.status}
                  onValueChange={(value: "active" | "inactive" | "error") =>
                    setCurrentProvider({ ...currentProvider, status: value })
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleEditProvider}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProviderManagement;
