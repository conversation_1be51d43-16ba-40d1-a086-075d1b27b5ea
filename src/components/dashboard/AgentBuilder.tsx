"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  PlusCircle,
  Trash2,
  Save,
  Play,
  Settings,
  Code,
  MoveHorizontal,
  ArrowRight,
  Upload,
} from "lucide-react";

interface Node {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    name: string;
    provider?: string;
    prompt?: string;
    settings?: Record<string, any>;
  };
}

interface Edge {
  id: string;
  source: string;
  target: string;
}

const AgentBuilder = () => {
  const [nodes, setNodes] = useState<Node[]>([
    {
      id: "1",
      type: "input",
      position: { x: 100, y: 100 },
      data: { name: "Start" },
    },
    {
      id: "2",
      type: "ai",
      position: { x: 300, y: 100 },
      data: {
        name: "Process Input",
        provider: "openai",
        prompt: "Analyze the user input and provide a helpful response.",
      },
    },
    {
      id: "3",
      type: "output",
      position: { x: 500, y: 100 },
      data: { name: "Response" },
    },
  ]);

  const [edges, setEdges] = useState<Edge[]>([
    { id: "e1-2", source: "1", target: "2" },
    { id: "e2-3", source: "2", target: "3" },
  ]);

  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [workflowName, setWorkflowName] = useState("New Agent Workflow");
  const [activeTab, setActiveTab] = useState("canvas");

  const handleAddNode = (type: string) => {
    const newNode: Node = {
      id: `${nodes.length + 1}`,
      type,
      position: { x: 200, y: 200 },
      data: { name: `New ${type.charAt(0).toUpperCase() + type.slice(1)}` },
    };
    setNodes([...nodes, newNode]);
    setSelectedNode(newNode);
  };

  const handleNodeSelect = (node: Node) => {
    setSelectedNode(node);
  };

  const handleNodeUpdate = (id: string, data: any) => {
    setNodes(
      nodes.map((node) =>
        node.id === id ? { ...node, data: { ...node.data, ...data } } : node,
      ),
    );
    if (selectedNode && selectedNode.id === id) {
      setSelectedNode({
        ...selectedNode,
        data: { ...selectedNode.data, ...data },
      });
    }
  };

  const handleDeleteNode = (id: string) => {
    setNodes(nodes.filter((node) => node.id !== id));
    setEdges(edges.filter((edge) => edge.source !== id && edge.target !== id));
    if (selectedNode && selectedNode.id === id) {
      setSelectedNode(null);
    }
  };

  const handleSaveWorkflow = () => {
    console.log("Saving workflow:", { name: workflowName, nodes, edges });
    // TODO: Implement save workflow logic
  };

  const handleTestWorkflow = () => {
    console.log("Testing workflow");
    // TODO: Implement test workflow logic
  };

  const handleDeployWorkflow = () => {
    console.log("Deploying workflow");
    // TODO: Implement deploy workflow logic
  };

  return (
    <div className="flex flex-col h-full bg-background">
      <div className="flex justify-between items-center p-4 border-b">
        <div className="flex items-center space-x-4">
          <Input
            value={workflowName}
            onChange={(e) => setWorkflowName(e.target.value)}
            className="font-semibold text-lg w-64"
          />
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" onClick={handleSaveWorkflow}>
              <Save className="mr-2 h-4 w-4" /> Save
            </Button>
            <Button size="sm" variant="outline" onClick={handleTestWorkflow}>
              <Play className="mr-2 h-4 w-4" /> Test
            </Button>
            <Button size="sm" variant="outline" onClick={handleDeployWorkflow}>
              <Upload className="mr-2 h-4 w-4" /> Deploy
            </Button>
          </div>
        </div>
        <div>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-[400px]"
            defaultValue="canvas">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="canvas">Canvas</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="code">Code</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      <div className="flex flex-1 overflow-hidden">
        <div className="w-64 border-r p-4 bg-muted/20 overflow-y-auto">
          <div className="mb-6">
            <h3 className="font-medium mb-2">Add Node</h3>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                onClick={() => handleAddNode("input")}
                className="justify-start"
              >
                <PlusCircle className="mr-2 h-4 w-4" /> Input
              </Button>
              <Button
                variant="outline"
                onClick={() => handleAddNode("ai")}
                className="justify-start"
              >
                <PlusCircle className="mr-2 h-4 w-4" /> AI
              </Button>
              <Button
                variant="outline"
                onClick={() => handleAddNode("tool")}
                className="justify-start"
              >
                <PlusCircle className="mr-2 h-4 w-4" /> Tool
              </Button>
              <Button
                variant="outline"
                onClick={() => handleAddNode("condition")}
                className="justify-start"
              >
                <PlusCircle className="mr-2 h-4 w-4" /> Condition
              </Button>
              <Button
                variant="outline"
                onClick={() => handleAddNode("output")}
                className="justify-start"
              >
                <PlusCircle className="mr-2 h-4 w-4" /> Output
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Nodes</h3>
            <div className="space-y-1">
              {nodes.map((node) => (
                <div
                  key={node.id}
                  className={`p-2 rounded cursor-pointer flex justify-between items-center ${selectedNode?.id === node.id ? "bg-accent text-accent-foreground" : "hover:bg-accent/50"}`}
                  onClick={() => handleNodeSelect(node)}
                >
                  <span>{node.data.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteNode(node.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="canvas" className="h-full">
            <div className="relative h-full bg-muted/10 overflow-auto p-4">
              {/* Canvas area - in a real implementation this would be a React Flow or similar component */}
              <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                {nodes.map((node) => (
                  <div
                    key={node.id}
                    className="absolute p-4 rounded-md border bg-card shadow-sm cursor-move"
                    style={{
                      left: `${node.position.x}px`,
                      top: `${node.position.y}px`,
                      minWidth: "120px",
                      minHeight: "60px",
                    }}
                    onClick={() => handleNodeSelect(node)}
                  >
                    <div className="text-sm font-medium">{node.data.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {node.type}
                    </div>
                  </div>
                ))}

                {/* Render edges */}
                <svg className="absolute inset-0 pointer-events-none">
                  {edges.map((edge) => {
                    const sourceNode = nodes.find((n) => n.id === edge.source);
                    const targetNode = nodes.find((n) => n.id === edge.target);

                    if (!sourceNode || !targetNode) return null;

                    const sourceX = sourceNode.position.x + 120; // Assuming node width
                    const sourceY = sourceNode.position.y + 30; // Half of node height
                    const targetX = targetNode.position.x;
                    const targetY = targetNode.position.y + 30; // Half of node height

                    return (
                      <g key={edge.id}>
                        <path
                          d={`M${sourceX},${sourceY} C${sourceX + 50},${sourceY} ${targetX - 50},${targetY} ${targetX},${targetY}`}
                          stroke="#64748b"
                          strokeWidth="2"
                          fill="none"
                          markerEnd="url(#arrowhead)"
                        />
                        <defs>
                          <marker
                            id="arrowhead"
                            markerWidth="10"
                            markerHeight="7"
                            refX="9"
                            refY="3.5"
                            orient="auto"
                          >
                            <polygon points="0 0, 10 3.5, 0 7" fill="#64748b" />
                          </marker>
                        </defs>
                      </g>
                    );
                  })}
                </svg>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="h-full p-4 overflow-auto">
            <Card>
              <CardHeader>
                <CardTitle>Workflow Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="workflow-name">Workflow Name</Label>
                      <Input
                        id="workflow-name"
                        value={workflowName}
                        onChange={(e) => setWorkflowName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="default-provider">Default Provider</Label>
                      <Select defaultValue="openai">
                        <SelectTrigger id="default-provider">
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="claude">Claude</SelectItem>
                          <SelectItem value="gemini">Gemini</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Describe the purpose of this workflow"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="debug-mode" />
                    <Label htmlFor="debug-mode">Enable Debug Mode</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="hitl" />
                    <Label htmlFor="hitl">Human-in-the-Loop</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="code" className="h-full p-4 overflow-auto">
            <Card>
              <CardHeader>
                <CardTitle>Generated Code</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="p-4 bg-muted rounded-md overflow-auto text-sm">
                  {JSON.stringify(
                    { workflow: workflowName, nodes, edges },
                    null,
                    2,
                  )}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
        </div>

        {selectedNode && (
          <div className="w-80 border-l p-4 bg-muted/20 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Node Properties</h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setSelectedNode(null)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="node-name">Name</Label>
                <Input
                  id="node-name"
                  value={selectedNode.data.name}
                  onChange={(e) =>
                    handleNodeUpdate(selectedNode.id, { name: e.target.value })
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="node-type">Type</Label>
                <Input id="node-type" value={selectedNode.type} disabled />
              </div>

              {selectedNode.type === "ai" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="ai-provider">Provider</Label>
                    <Select
                      value={selectedNode.data.provider || "openai"}
                      onValueChange={(value) =>
                        handleNodeUpdate(selectedNode.id, { provider: value })
                      }
                    >
                      <SelectTrigger id="ai-provider">
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="openai">OpenAI</SelectItem>
                        <SelectItem value="claude">Claude</SelectItem>
                        <SelectItem value="gemini">Gemini</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="ai-prompt">Prompt</Label>
                    <Textarea
                      id="ai-prompt"
                      value={selectedNode.data.prompt || ""}
                      onChange={(e) =>
                        handleNodeUpdate(selectedNode.id, {
                          prompt: e.target.value,
                        })
                      }
                      rows={5}
                      placeholder="Enter prompt template..."
                    />
                  </div>
                </>
              )}

              {selectedNode.type === "tool" && (
                <div className="space-y-2">
                  <Label htmlFor="tool-type">Tool Type</Label>
                  <Select defaultValue="api">
                    <SelectTrigger id="tool-type">
                      <SelectValue placeholder="Select tool type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="api">API Call</SelectItem>
                      <SelectItem value="function">Function</SelectItem>
                      <SelectItem value="database">Database Query</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {selectedNode.type === "condition" && (
                <div className="space-y-2">
                  <Label htmlFor="condition-expression">
                    Condition Expression
                  </Label>
                  <Textarea
                    id="condition-expression"
                    placeholder="Enter condition expression..."
                    rows={3}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentBuilder;
