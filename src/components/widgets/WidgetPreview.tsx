"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ColorSwatch } from "./ColorSwatch";
import { MessageSquare, Code, Settings, Copy, Check } from "lucide-react";

interface WidgetPreviewProps {
  widgetId?: string;
  initialTheme?: WidgetTheme;
  initialDomains?: string[];
}

interface WidgetTheme {
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  borderRadius: string;
}

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

const WidgetPreview = ({
  widgetId = "widget-123",
  initialTheme = {
    primaryColor: "#7C3AED",
    backgroundColor: "#FFFFFF",
    textColor: "#1F2937",
    fontFamily: "Inter, sans-serif",
    borderRadius: "8px",
  },
  initialDomains = ["example.com"],
}: WidgetPreviewProps) => {
  const [activeTab, setActiveTab] = useState("preview");
  const [theme, setTheme] = useState<WidgetTheme>(initialTheme);
  const [domains, setDomains] = useState<string[]>(initialDomains);
  const [newDomain, setNewDomain] = useState("");
  const [embedCode, setEmbedCode] = useState("");
  const [copied, setCopied] = useState(false);
  const [chatInput, setChatInput] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([
    { role: "assistant", content: "Hello! How can I help you today?" },
  ]);
  const [isWidgetOpen, setIsWidgetOpen] = useState(true);

  // Generate embed code based on current settings
  React.useEffect(() => {
    const code = `<script>
  (function(w, d) {
    const script = d.createElement('script');
    script.src = 'https://synapse-ai.example.com/widget/${widgetId}.js';
    script.async = true;
    script.setAttribute('data-theme', '${JSON.stringify(theme)}');
    script.setAttribute('data-allowed-domains', '${domains.join(",")}');
    d.head.appendChild(script);
  })(window, document);
</script>`;

    setEmbedCode(code);
  }, [widgetId, theme, domains]);

  const handleSendMessage = () => {
    if (!chatInput.trim()) return;

    // Add user message
    const newMessages = [...messages, { role: "user", content: chatInput }];
    setMessages(newMessages);
    setChatInput("");

    // Simulate AI response after a short delay
    setTimeout(() => {
      setMessages([
        ...newMessages,
        {
          role: "assistant",
          content:
            "This is a simulated response from the AI assistant. In a real implementation, this would be the response from your configured AI agent.",
        },
      ]);
    }, 1000);
  };

  const handleAddDomain = () => {
    if (newDomain && !domains.includes(newDomain)) {
      setDomains([...domains, newDomain]);
      setNewDomain("");
    }
  };

  const handleRemoveDomain = (domain: string) => {
    setDomains(domains.filter((d) => d !== domain));
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(embedCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const updateTheme = (key: keyof WidgetTheme, value: string) => {
    setTheme({ ...theme, [key]: value });
  };

  return (
    <div className="bg-background w-full h-full p-4">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Widget Preview</CardTitle>
          <CardDescription>
            Customize and preview your AI widget before embedding it on your
            website.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Preview
              </TabsTrigger>
              <TabsTrigger
                value="customize"
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Customize
              </TabsTrigger>
              <TabsTrigger value="embed" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Embed Code
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="space-y-4">
              <div className="flex justify-end mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsWidgetOpen(!isWidgetOpen)}
                >
                  {isWidgetOpen ? "Close Widget" : "Open Widget"}
                </Button>
              </div>

              {isWidgetOpen && (
                <div
                  className="border rounded-lg overflow-hidden shadow-lg"
                  style={{
                    backgroundColor: theme.backgroundColor,
                    color: theme.textColor,
                    fontFamily: theme.fontFamily,
                    borderRadius: theme.borderRadius,
                  }}
                >
                  <div
                    className="p-3 font-medium"
                    style={{
                      backgroundColor: theme.primaryColor,
                      color: "white",
                    }}
                  >
                    SynapseAI Assistant
                  </div>

                  <div className="h-80 overflow-y-auto p-4 space-y-4">
                    {messages.map((message, index) => (
                      <div
                        key={index}
                        className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                      >
                        <div
                          className={`max-w-[80%] p-3 rounded-lg ${
                            message.role === "user"
                              ? "bg-opacity-10"
                              : "bg-opacity-5"
                          }`}
                          style={{
                            backgroundColor:
                              message.role === "user"
                                ? theme.primaryColor
                                : "#f0f0f0",
                            color:
                              message.role === "user"
                                ? "white"
                                : theme.textColor,
                          }}
                        >
                          {message.content}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="p-3 border-t flex gap-2">
                    <Input
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                      placeholder="Type your message..."
                      onKeyPress={(e) =>
                        e.key === "Enter" && handleSendMessage()
                      }
                      style={{ color: theme.textColor }}
                      className="flex-1"
                    />
                    <Button
                      onClick={handleSendMessage}
                      style={{ backgroundColor: theme.primaryColor }}
                    >
                      Send
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="customize" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <ColorSwatch
                        color={theme.primaryColor}
                        onChange={(color) => updateTheme("primaryColor", color)}
                      />
                      <Input
                        id="primaryColor"
                        value={theme.primaryColor}
                        onChange={(e) =>
                          updateTheme("primaryColor", e.target.value)
                        }
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="backgroundColor">Background Color</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <ColorSwatch
                        color={theme.backgroundColor}
                        onChange={(color) =>
                          updateTheme("backgroundColor", color)
                        }
                      />
                      <Input
                        id="backgroundColor"
                        value={theme.backgroundColor}
                        onChange={(e) =>
                          updateTheme("backgroundColor", e.target.value)
                        }
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="textColor">Text Color</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <ColorSwatch
                        color={theme.textColor}
                        onChange={(color) => updateTheme("textColor", color)}
                      />
                      <Input
                        id="textColor"
                        value={theme.textColor}
                        onChange={(e) =>
                          updateTheme("textColor", e.target.value)
                        }
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="fontFamily">Font Family</Label>
                    <Input
                      id="fontFamily"
                      value={theme.fontFamily}
                      onChange={(e) =>
                        updateTheme("fontFamily", e.target.value)
                      }
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="borderRadius">Border Radius</Label>
                    <Input
                      id="borderRadius"
                      value={theme.borderRadius}
                      onChange={(e) =>
                        updateTheme("borderRadius", e.target.value)
                      }
                      className="mt-1"
                    />
                  </div>

                  <div className="pt-4">
                    <Label className="mb-2 block">Domain Restrictions</Label>
                    <div className="flex gap-2 mb-2">
                      <Input
                        value={newDomain}
                        onChange={(e) => setNewDomain(e.target.value)}
                        placeholder="example.com"
                        className="flex-1"
                      />
                      <Button onClick={handleAddDomain}>Add</Button>
                    </div>

                    <div className="space-y-2 mt-2">
                      {domains.map((domain, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between bg-muted p-2 rounded"
                        >
                          <span>{domain}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveDomain(domain)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      {domains.length === 0 && (
                        <p className="text-sm text-muted-foreground">
                          No domain restrictions (widget will work on any
                          domain)
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="embed" className="space-y-4">
              <div>
                <Label htmlFor="embedCode" className="mb-2 block">
                  Embed Code
                </Label>
                <div className="relative">
                  <Textarea
                    id="embedCode"
                    value={embedCode}
                    readOnly
                    className="font-mono h-48"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={handleCopyCode}
                  >
                    {copied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Add this code to your website to embed the SynapseAI widget.
                  The widget will only work on the domains you've specified in
                  the customization tab.
                </p>
              </div>

              <div className="pt-4">
                <h3 className="text-lg font-medium mb-2">
                  Installation Instructions
                </h3>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Copy the embed code above</li>
                  <li>
                    Paste it into the{" "}
                    <code className="bg-muted px-1 rounded">&lt;head&gt;</code>{" "}
                    or{" "}
                    <code className="bg-muted px-1 rounded">&lt;body&gt;</code>{" "}
                    section of your HTML
                  </li>
                  <li>The widget will automatically initialize on your page</li>
                  <li>
                    Users can interact with your AI assistant directly on your
                    website
                  </li>
                </ol>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => {
              setTheme(initialTheme);
              setDomains(initialDomains);
            }}
          >
            Reset to Default
          </Button>
          <Button
            onClick={async () => {
              try {
                const token = localStorage.getItem("accessToken");
                if (!token) {
                  alert("Please log in to save configuration");
                  return;
                }

                const response = await fetch("/api/widgets", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                  },
                  body: JSON.stringify({
                    id: widgetId,
                    name: "Custom Widget",
                    theme,
                    domains,
                    settings: {
                      embedCode,
                      lastUpdated: Date.now(),
                    },
                  }),
                });

                if (response.ok) {
                  alert("Configuration saved successfully!");
                } else {
                  const error = await response.json();
                  alert(`Failed to save: ${error.error || "Unknown error"}`);
                }
              } catch (error) {
                console.error("Save error:", error);
                alert("Failed to save configuration. Please try again.");
              }
            }}
          >
            Save Configuration
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default WidgetPreview;
