// Simple Express server to test auth functionality
const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
app.use(express.json());

// Auth configuration
const JWT_SECRET = "synapse-ai-secret-key";
const JWT_EXPIRES_IN = "24h";

// Role permissions
const ROLE_PERMISSIONS = {
  admin: [
    "admin:tenants", "admin:system", "admin:users",
    "widget:create", "widget:read", "widget:update", "widget:delete",
    "agent:create", "agent:read", "agent:update", "agent:delete", "agent:deploy",
    "provider:create", "provider:read", "provider:update", "provider:delete", "provider:manage",
    "analytics:read", "analytics:export"
  ],
  developer: [
    "widget:create", "widget:read", "widget:update", "widget:delete",
    "agent:create", "agent:read", "agent:update", "agent:delete", "agent:deploy",
    "provider:read", "provider:update",
    "analytics:read"
  ],
  user: [
    "widget:read",
    "agent:read",
    "analytics:read"
  ]
};

// Auth functions
function generateTokens(user) {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    tenantId: user.tenantId,
    permissions: user.permissions,
  };

  const accessToken = jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: "synapse-ai",
    audience: "synapse-ai-client",
  });

  const refreshToken = jwt.sign(
    { userId: user.id, type: "refresh" },
    JWT_SECRET,
    {
      expiresIn: "7d",
      issuer: "synapse-ai",
      audience: "synapse-ai-client",
    }
  );

  return { accessToken, refreshToken };
}

function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: "synapse-ai",
      audience: "synapse-ai-client",
    });
  } catch (error) {
    return null;
  }
}

// Routes
app.post('/auth/register', async (req, res) => {
  try {
    const { email, password, role = 'user', tenantId = 'default' } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password required' });
    }

    const hashedPassword = await bcrypt.hash(password, 12);
    const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const user = {
      id: userId,
      email,
      password: hashedPassword,
      role,
      tenantId,
      permissions: ROLE_PERMISSIONS[role] || [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isActive: true
    };

    const tokens = generateTokens(user);

    res.json({
      user: { ...user, password: undefined }, // Don't return password
      accessToken: tokens.accessToken
    });
  } catch (error) {
    res.status(500).json({ error: 'Registration failed' });
  }
});

app.post('/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password required' });
    }

    // For demo purposes, create a test user
    const testUser = {
      id: 'test-user-123',
      email: email,
      password: await bcrypt.hash('password123', 12),
      role: 'user',
      tenantId: 'default',
      permissions: ROLE_PERMISSIONS.user,
      isActive: true
    };

    const isValidPassword = await bcrypt.compare(password, testUser.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const tokens = generateTokens(testUser);

    res.json({
      user: { ...testUser, password: undefined },
      accessToken: tokens.accessToken
    });
  } catch (error) {
    res.status(500).json({ error: 'Login failed' });
  }
});

app.get('/auth/verify', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    res.json({ valid: true, payload });
  } catch (error) {
    res.status(500).json({ error: 'Verification failed' });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Auth service is running' });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 Auth test server running on port ${PORT}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   POST http://localhost:${PORT}/auth/register`);
  console.log(`   POST http://localhost:${PORT}/auth/login`);
  console.log(`   GET  http://localhost:${PORT}/auth/verify`);
});
