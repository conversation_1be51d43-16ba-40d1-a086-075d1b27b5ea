{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@swc/core": "^1.12.14", "@swc/helpers": "^0.5.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "autoprefixer": "10.4.20", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "next": "14.2.23", "next-themes": "^0.2.1", "prettier": "^3.3.3", "radix-ui": "^1.1.3", "react": "^18", "react-colorful": "^5.6.1", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^17.6.0", "tempo-devtools": "^2.0.108", "vaul": "^1.1.2", "zod": "^3.20.6"}, "devDependencies": {"@types/node": "^20.19.7", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}